version: 1
disable_existing_loggers: False
formatters:
  uvicorn:
    format: '{"time": "%(asctime)s",
            "source": "uvicorn",
            "pid": %(process)d,
            "level": "%(levelname)s",
            "filename": "%(filename)s",
            "function": "%(funcName)s",
            "message": "%(message)s"}'
    datefmt: '%Y-%m-%d %H:%M:%S'
handlers:
  console:
    class: logging.StreamHandler
    formatter: uvicorn
    stream: ext://sys.stdout
loggers:
  uvicorn:
    handlers: [console]
    propagate: no
