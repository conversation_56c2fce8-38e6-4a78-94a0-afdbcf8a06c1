ifneq (,$(wildcard ./.env))
	include .env
endif

ENV_PREFIX = stage
ENVIRONMENT = ${ENV_PREFIX}-ga
SERVICE_IMAGE_NAME = occupancy-app

.PHONY: help install-git-precommit-hook setup-env add-hosts unit-tests-run docker-build docker-delete docker-prune docker-up docker-down start stop install uninstall l10n-extract l10n-init l10n-compile

ifneq (,$(wildcard ./.env))
    include .env
    export $(shell sed 's/=.*//' .env)
endif

help: ## Available commands
	@fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep | sed -e 's/:.*##\s*/##/g' | awk -F'##' '{ printf "%-14s %s\n", $$1, $$2 }'

install-precommit-hook: # if host machines has python3, install and configure pre-commit
	command -v python3 >/dev/null 2>&1
	@echo Python 3 is installed
	pip install pre-commit
	pre-commit install

setup-env: ## Setup environment file
ifeq (, $(shell which envsubst))
	$(error "No envsubst in $(PATH), consider doing apt-get install gettext-base (https://command-not-found.com/envsubst)")
endif
	@$(eval LAUNCH_DARKLY_SDK_KEY=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/ld-relay/env --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.sdk_key'))
	DATABASE_HOST_READER='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/data-service/dot-env-file field=DI_DB_HOST)' \
	DATABASE_NAME='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/data-service/dot-env-file field=DI_DB_NAME)' \
	DATABASE_PORT='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/data-service/dot-env-file field=DI_DB_PORT)' \
	DATABASE_USER='$(shell $(MAKE) aws ssm-get-parameter --name "/data/${ENVIRONMENT}/postgresql/occupancy_user" --with-decryption --output json | jq -r '.Parameter.Value')' \
	DATABASE_PASSWORD='$(shell $(MAKE) aws ssm-get-parameter --name "/data/${ENVIRONMENT}/postgresql/occupancy_password" --with-decryption --output json | jq -r '.Parameter.Value')' \
	LAUNCH_DARKLY_SDK_KEY='$(LAUNCH_DARKLY_SDK_KEY)' \
	envsubst < .env.example > .env

aws-ssm-get:
	@aws ssm get-parameter --name '$(name)' --with-decryption --output json | jq -r '.Parameter.Value | fromjson | .$(field)'

docker.build: ## Docker build
	docker compose build

docker.delete: ## Docker delete images, volumes and its dependencies
	@docker compose down --volumes

docker.prune: ## Docker image prune
	@docker image prune --force

docker.up: ## Docker compose up
	@docker compose up occupancy-database occupancy-redis occupancy-app

docker.down: ## Docker compose down
	@docker compose down

start: docker.up ## Start application

stop: docker.down ## Stop application

install: setup-env docker.build install-precommit-hook migrations-upgrade ## Install application

uninstall: docker.delete docker.prune ## Uninstall application and its dependencies (images, volumes, networks)

recreate: uninstall install ## Recreate application

lint: ## Run Lint
	@uv run pre-commit run --all-files

unit-test: ## Make Unit test
	@uv run -m coverage run -m pytest -vv -s tests/unit && uv run -m coverage report && uv run coverage html

test:
	@uv run run -m pytest -vv -s tests/unit

sync: ## Sync the dependencies to venv
	@uv sync

migrations-upgrade: ## Upgrade migrations make migrations-upgrade version="VERSION" --Optionally add version to migrate to (other default to head)
	@docker exec -it $(SERVICE_IMAGE_NAME) alembic -c alembic.ini upgrade $(if $(version), $(version) $(else), head)

migrations-downgrade: ## Dowgrade migrations make migrations-downgrade version="VERSION" --Optionally add version to downgrade to (other default to base)
	@docker exec -it $(SERVICE_IMAGE_NAME) alembic -c alembic.ini downgrade $(if $(version), $(version) $(else), base)

migrations-revision: ## Create revisions make migrations-revision message="MESSAGE" - Optionally define the migration message
	@docker exec -it $(SERVICE_IMAGE_NAME) alembic -c alembic.ini revision $(if $(message), -m "$(message)" $(else))

migrations-revision-autogenerate: ## Create revisions make migrations-revision-autogenerate message="MESSAGE" - Optionally define the migration message
	@docker exec -it $(SERVICE_IMAGE_NAME) alembic -c alembic.ini revision --autogenerate $(if $(message), -m "$(message)" $(else))

run-api: ## Run API
	uv run fastapi dev

run-grpc: ## Run gRPC
	.venv/bin/watchmedo auto-restart --recursive --pattern="*.py" --directory="app" uv -- run app/grpc/server.py

generate-calendar: ## Generate calendar
	uv run bin/generate_calendar.py

l10n-extract: ## Extract translation strings into messages.pot
	@pybabel extract -F babel.cfg -o messages.pot .

l10n-init: ## Initialize a new language, e.g., make l10n-init lang=fa
	@test -n "$(lang)" || (echo "Usage: make l10n-init lang=<language_code>" && exit 1)
	@pybabel init -i messages.pot -d app/lang -l $(lang)

l10n-compile: ## Compile all translations
	@pybabel compile -d app/lang

l10n-init-all: ## Initialize all supported languages
	@for l in en es es_ES pt_BR pt_PT ru th de it fr ar ach ca id; do \
		echo "Initializing language: $$l"; \
		pybabel init -i messages.pot -d app/lang -l $$l || true; \
	done
