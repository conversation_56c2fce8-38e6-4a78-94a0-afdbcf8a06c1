from glob import glob


def convert_to_python_path(string: str) -> str:
    return string.replace("/", ".").replace("\\", ".").replace(".py", "")


pytest_plugins = [
    convert_to_python_path(fixture)
    for fixture in glob("tests/unit/fixtures/**/*.py", recursive=True)
]


class AsyncMockSession:
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        pass

    async def execute(self, *args, **kwargs):
        class Result:
            def all(self):
                return []

        return Result()
