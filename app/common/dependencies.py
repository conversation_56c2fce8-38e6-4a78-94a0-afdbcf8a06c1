from http import HTTPStatus

from fastapi import Depends, <PERSON><PERSON>, HTTPException, Query
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer

from app.common.logger import logger
from app.services.token import TokenService
from app.services.user_service import UserService


def authorization(
    organizationId: int = Header(
        alias="x-organization-id", description="Organization ID"
    ),
    propertyIds: list[int] = Query(alias="propertyIds", description="Property IDs"),
    token: HTTPAuthorizationCredentials = Depends(
        HTTPBearer(
            bearerFormat="JWT",
            scheme_name="Authorization",
            description="Token",
            auto_error=False,
        )
    ),
):
    if not organizationId:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail="x-organization-id header is missing",
        )

    if not propertyIds:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail="propertyIds query param is missing or empty",
        )

    if not token:
        raise HTTPException(
            status_code=HTTPStatus.UNAUTHORIZED, detail="Access token is missing"
        )

    token_service = TokenService(token.credentials)
    if token_service.is_super_admin():
        return dict(organization_id=organizationId, token=token)

    property_ids, organization_ids = UserService.get_user_assignment(
        token_service.get_user_id()
    )
    logger.info(
        f"User {token_service.get_user_id()} has access to properties: {property_ids} and organizations: {organization_ids}"
    )

    if organizationId not in organization_ids:
        raise HTTPException(
            status_code=HTTPStatus.UNAUTHORIZED,
            detail=f"Organization ID {organizationId} provided is not authorized via access token",
        )

    for propertyId in propertyIds:
        if propertyId not in property_ids:
            raise HTTPException(
                status_code=HTTPStatus.UNAUTHORIZED,
                detail=f"Property ID {propertyId} provided is not authorized via access token",
            )

    return dict(organization_id=organizationId, token=token)
