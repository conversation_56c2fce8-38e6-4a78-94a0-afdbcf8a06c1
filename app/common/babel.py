import os

from fastapi_babel import Babel, BabelConfigs
from fastapi_babel.local_context import BabelContext

babel_configs = BabelConfigs(
    ROOT_DIR=os.path.dirname(os.path.abspath(__file__)),
    BABEL_DEFAULT_LOCALE="en",
    BABEL_TRANSLATION_DIRECTORY="lang",
)


def with_locale(locale: str):
    babel = Babel(configs=babel_configs)
    babel.locale = locale
    return BabelContext(babel_configs, babel=babel)


if __name__ == "__main__":
    babel: Babel = Babel(configs=babel_configs)
    babel.run_cli()
