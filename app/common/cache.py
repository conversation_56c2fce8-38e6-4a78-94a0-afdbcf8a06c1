import functools
import json
from datetime import date, datetime
from enum import Enum

import redis.asyncio as redis

from app.settings import settings

# Cache timeout constants
TIMEOUT_MINUTE = 60


class Cache:
    def __init__(self):
        self.cache = redis.StrictRedis(
            host=settings.REDIS_HOST, port=settings.REDIS_PORT
        )

    async def get(self, key):
        result = await self.cache.get(key)
        if result:
            return json.loads(result)
        return None

    async def set(self, key, value, ex=None):
        await self.cache.set(key, json.dumps(value), ex=ex)

    def memoize(self, ex=None):
        def decorator(func):
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                instance = args[0] if args and hasattr(args[0], func.__name__) else None
                args_repr = args[1:] if instance else args

                def serialize(obj):
                    if isinstance(obj, Enum):
                        return obj.value
                    if isinstance(obj, (date, datetime)):
                        return obj.isoformat()
                    if isinstance(obj, list):
                        return "_".join(str(serialize(i)) for i in obj) if obj else None
                    if obj is None or obj == "":
                        return None
                    return str(obj)

                key_parts = []

                if instance:
                    for field in ("organization_id", "property_ids"):
                        val = serialize(getattr(instance, field, None))
                        if val:
                            key_parts.append(f"{field}={val}")

                for idx, arg in enumerate(args_repr):
                    val = serialize(arg)
                    if val:
                        key_parts.append(f"arg{idx}={val}")

                for k, v in sorted(kwargs.items()):
                    val = serialize(v)
                    if val:
                        key_parts.append(f"{k}={val}")

                key = "|".join(key_parts)

                result = await self.get(key)
                if result is not None:
                    return result

                result = await func(*args, **kwargs)
                await self.set(key, result, ex=ex)
                return result

            return wrapper

        return decorator


cache = Cache()
