import re
from typing import TYPE_CHECKING  # noqa:F401
from typing import List  # noqa:F401
from typing import Optional  # noqa:F401
from typing import Union  # noqa:F401

from ddtrace.ext import http
from ddtrace.trace import TraceFilter


class FilterRequestsOnUrl(TraceFilter):
    r"""Filter out traces from incoming http requests based on the request's url.

    This class takes as argument a list of regular expression patterns
    representing the urls to be excluded from tracing. A trace will be excluded
    if its root span contains a ``http.url`` tag and if this tag matches any of
    the provided regular expression using the standard python regexp match
    semantic (https://docs.python.org/3/library/re.html#re.match).

    :param list regexps: a list of regular expressions (or a single string) defining
                         the urls that should be filtered out.

    Examples:
    To filter out http calls to domain api.example.com::

        FilterRequestsOnUrl(r'http://api\\.example\\.com')

    To filter out http calls to all first level subdomains from example.com::

        FilterRequestOnUrl(r'http://.*+\\.example\\.com')

    To filter out calls to both http://test.example.com and http://example.com/healthcheck::

        FilterRequestOnUrl([r'http://test\\.example\\.com', r'http://example\\.com/healthcheck'])
    """

    def __init__(self, regexps: Union[str, List[str]]):
        if isinstance(regexps, str):
            regexps = [regexps]
        self._regexps = [re.compile(regexp) for regexp in regexps]

    def process_trace(self, trace):
        # type: (List[Span]) -> Optional[List[Span]]
        """
        When the filter is registered in the tracer, process_trace is called by
        on each trace before it is sent to the agent, the returned value will
        be fed to the next filter in the list. If process_trace returns None,
        the whole trace is discarded.
        """

        for span in trace:
            url = span.get_tag(http.URL)
            if span.parent_id is None and url is not None:
                for regexp in self._regexps:
                    if regexp.match(url):
                        return None
        return trace


class FilterRequestsOnGrpcMethod(TraceFilter):
    r"""Filter out traces from incoming grpc requests based on the full grpc path

    This class takes as argument a list of grpc paths.

    For example, /grpc.health.v1.Health/Check.

    :param list strings: a list of strings defining the grpc paths that should be filtered out.

    Examples:
    To filter out http calls to domain api.example.com::

        FilterRequestsOnGrpcMethod(r'/grpc.health.v1.Health/Check')
    """

    def __init__(self, paths: Union[str, List[str]]):
        self._paths = paths

    def process_trace(self, trace):
        # type: (List[Span]) -> Optional[List[Span]]
        """
        When the filter is registered in the tracer, process_trace is called by
        on each trace before it is sent to the agent, the returned value will
        be fed to the next filter in the list. If process_trace returns None,
        the whole trace is discarded.
        """

        for span in trace:
            path = span.get_tag("grpc.method.path")
            if span.parent_id is None and path is not None:
                for _path in self._paths:
                    if _path == path:
                        return None
        return trace
