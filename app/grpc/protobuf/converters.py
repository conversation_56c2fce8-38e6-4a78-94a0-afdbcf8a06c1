import datetime

from cloudbeds.occupancy.v1 import occupancy_pb2
from cloudbeds.rpc.v1 import common_pb2
from cloudbeds.type.v1 import l10n_pb2
from google.type import date_pb2

from app.enums.column import Column
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.enums.sort import Sort
from app.models.occupancy import OccupancyAndRevenue

GROUP_MAPPING = {
    OccupancyAndRevenue.__table__.columns.stay_date.key: occupancy_pb2.GROUP_BY_STAY_DATE,
    OccupancyAndRevenue.__table__.columns.room_type.key: occupancy_pb2.GROUP_BY_ROOM_TYPE,
    OccupancyAndRevenue.__table__.columns.reservation_source.key: occupancy_pb2.GROUP_BY_RESERVATION_SOURCE,
    OccupancyAndRevenue.__table__.columns.group_profile_code.key: occupancy_pb2.GROUP_BY_GROUP_PROFILE_CODE,
}

COLUMN_MAPPING = {
    OccupancyAndRevenue.__table__.columns.stay_date.key: occupancy_pb2.COLUMN_STAY_DATE,
    OccupancyAndRevenue.__table__.columns.organization_id.key: occupancy_pb2.COLUMN_ORGANIZATION_ID,
    OccupancyAndRevenue.__table__.columns.property_id.key: occupancy_pb2.COLUMN_PROPERTY_ID,
    OccupancyAndRevenue.__table__.columns.room_type_id.key: occupancy_pb2.COLUMN_ROOM_TYPE_ID,
    OccupancyAndRevenue.__table__.columns.room_type.key: occupancy_pb2.COLUMN_ROOM_TYPE,
    OccupancyAndRevenue.__table__.columns.room_id.key: occupancy_pb2.COLUMN_ROOM_ID,
    OccupancyAndRevenue.__table__.columns.room_type_short_title.key: occupancy_pb2.COLUMN_ROOM_TYPE_SHORT_TITLE,
    OccupancyAndRevenue.__table__.columns.room_name.key: occupancy_pb2.COLUMN_ROOM_NAME,
    OccupancyAndRevenue.occupancy.key: occupancy_pb2.COLUMN_OCCUPANCY,
    OccupancyAndRevenue.__table__.columns.capacity_count.key: occupancy_pb2.COLUMN_CAPACITY_COUNT,
    OccupancyAndRevenue.__table__.columns.total_rooms_sold.key: occupancy_pb2.COLUMN_TOTAL_ROOMS_SOLD,
    OccupancyAndRevenue.__table__.columns.total_rooms_available.key: occupancy_pb2.COLUMN_TOTAL_ROOMS_AVAILABLE,
    OccupancyAndRevenue.__table__.columns.blocked_rooms_count.key: occupancy_pb2.COLUMN_BLOCKED_ROOMS_COUNT,
    OccupancyAndRevenue.__table__.columns.out_of_service_count.key: occupancy_pb2.COLUMN_OUT_OF_SERVICE_COUNT,
    OccupancyAndRevenue.__table__.columns.total_room_rate.key: occupancy_pb2.COLUMN_TOTAL_ROOM_RATE,
    OccupancyAndRevenue.__table__.columns.total_room_revenue.key: occupancy_pb2.COLUMN_TOTAL_ROOM_REVENUE,
    OccupancyAndRevenue.__table__.columns.total_other_room_revenue.key: occupancy_pb2.COLUMN_TOTAL_OTHER_ROOM_REVENUE,
    OccupancyAndRevenue.__table__.columns.total_other_revenue.key: occupancy_pb2.COLUMN_TOTAL_OTHER_REVENUE,
    OccupancyAndRevenue.__table__.columns.total_room_revenue_adjustments.key: occupancy_pb2.COLUMN_TOTAL_ROOM_REVENUE_ADJUSTMENTS,
    OccupancyAndRevenue.__table__.columns.total_room_taxes.key: occupancy_pb2.COLUMN_TOTAL_ROOM_TAXES,
    OccupancyAndRevenue.__table__.columns.total_room_fees.key: occupancy_pb2.COLUMN_TOTAL_ROOM_FEES,
    OccupancyAndRevenue.revpar.key: occupancy_pb2.COLUMN_REVPAR,
    OccupancyAndRevenue.adr.key: occupancy_pb2.COLUMN_ADR,
    OccupancyAndRevenue.__table__.columns.guest_count.key: occupancy_pb2.COLUMN_GUEST_COUNT,
    OccupancyAndRevenue.__table__.columns.adults_count.key: occupancy_pb2.COLUMN_ADULTS_COUNT,
    OccupancyAndRevenue.__table__.columns.children_count.key: occupancy_pb2.COLUMN_CHILDREN_COUNT,
    OccupancyAndRevenue.__table__.columns.group_profile.key: occupancy_pb2.COLUMN_GROUP_PROFILE,
    OccupancyAndRevenue.__table__.columns.group_profile_id.key: occupancy_pb2.COLUMN_GROUP_PROFILE_ID,
    OccupancyAndRevenue.__table__.columns.group_profile_code.key: occupancy_pb2.COLUMN_GROUP_PROFILE_CODE,
}

GRANULARITY_MAPPING = {
    Granularity.day: occupancy_pb2.GRANULARITY_DAY,
    Granularity.month: occupancy_pb2.GRANULARITY_MONTH,
    Granularity.year: occupancy_pb2.GRANULARITY_YEAR,
}

LANGUAGE_ENUM_TO_LOCALE = {
    l10n_pb2.LANGUAGE_EN_US: "en_US",
    l10n_pb2.LANGUAGE_ES_MX: "es_MX",
    l10n_pb2.LANGUAGE_ES_ES: "es_ES",
    l10n_pb2.LANGUAGE_PT_BR: "pt_BR",
    l10n_pb2.LANGUAGE_PT_PT: "pt_PT",
    l10n_pb2.LANGUAGE_RU_RU: "ru_RU",
    l10n_pb2.LANGUAGE_TH_TH: "th_TH",
    l10n_pb2.LANGUAGE_DE_DE: "de_DE",
    l10n_pb2.LANGUAGE_IT_IT: "it_IT",
    l10n_pb2.LANGUAGE_FR_FR: "fr_FR",
    l10n_pb2.LANGUAGE_AR_SA: "ar_SA",
    l10n_pb2.LANGUAGE_CA_ES: "ca_ES",
    l10n_pb2.LANGUAGE_ID_ID: "id_ID",
    l10n_pb2.LANGUAGE_ACH: "ach",
}


def to_python_date(date: date_pb2.Date) -> datetime.date:
    return datetime.date(date.year, date.month, date.day)


def to_proto_date(date: datetime.date) -> date_pb2.Date:
    return date_pb2.Date(year=date.year, month=date.month, day=date.day)


def granularity_proto_to_enum(
    granularity: occupancy_pb2.Granularity,
) -> Granularity | None:
    if granularity == occupancy_pb2.GRANULARITY_UNSPECIFIED:
        return None
    return next(
        (
            granularity_enum
            for granularity_enum, proto_enum in GRANULARITY_MAPPING.items()
            if proto_enum == granularity
        ),
        None,
    )


def granularity_enum_to_proto(granularity: Granularity) -> occupancy_pb2.Granularity:
    return GRANULARITY_MAPPING.get(
        granularity, ValueError(f"unknown granularity {granularity}")
    )


def group_by_proto_to_enum(group_by: occupancy_pb2.GroupBy) -> GroupBy | None:
    if group_by == occupancy_pb2.GROUP_BY_UNSPECIFIED:
        return None
    return next(
        (
            group_by_enum
            for group_by_enum, proto_enum in GROUP_MAPPING.items()
            if proto_enum == group_by
        ),
        None,
    )


def group_by_enum_to_proto(group_by: str) -> occupancy_pb2.GroupBy:
    return GROUP_MAPPING.get(group_by, ValueError(f"unknown group_by {group_by}"))


def column_proto_to_enum(proto_column: occupancy_pb2.Column) -> Column | None:
    if proto_column == occupancy_pb2.COLUMN_UNSPECIFIED:
        return None
    return next(
        (
            column
            for column in Column
            if COLUMN_MAPPING.get(column.value) == proto_column
        ),
        None,
    )


def column_enum_to_proto(column: str) -> occupancy_pb2.Column:
    return COLUMN_MAPPING.get(column, ValueError(f"unknown column {column}"))


def order_by_proto_to_enum(order_by: occupancy_pb2.OrderBy) -> list[Sort]:
    sorts = []

    for order in order_by:
        field_name = occupancy_pb2.OrderBy.Field.Name(order.field)
        direction_name = common_pb2.Direction.Name(order.direction)
        field = field_name.lower().replace("field_", "")
        direction = direction_name.lower().replace("direction_", "")
        sort = f"{field}:{direction}"
        sorts.append(Sort(sort))

    return sorts


def grpc_language_to_locale(lang_enum: l10n_pb2.Language) -> str:
    return LANGUAGE_ENUM_TO_LOCALE.get(lang_enum, "en_US")
