from cloudbeds.occupancy.v1 import occupancy_pb2, occupancy_pb2_grpc

from app.grpc.service.occupancy import OccupancyService


class OccupancyServiceServicer(occupancy_pb2_grpc.OccupancyServiceServicer):
    async def Query(self, request: occupancy_pb2.QueryRequest, context):
        return await OccupancyService().query(request)

    async def QueryReservationSources(
        self, request: occupancy_pb2.QueryReservationSourcesRequest, context
    ):
        return await OccupancyService().query_reservation_sources(request)
