from typing import Any, Dict, List

from cloudbeds.occupancy.v1 import occupancy_pb2
from grpc_interceptor.exceptions import InvalidArgument
from pydantic import ValidationError

from app.common.babel import with_locale
from app.common.constants import DEFAULT_PAGINATION_LIMIT
from app.common.logger import logger
from app.database.session import get_session
from app.enums.column import Column
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.grpc.protobuf.converters import (
    column_proto_to_enum,
    granularity_proto_to_enum,
    group_by_proto_to_enum,
    grpc_language_to_locale,
    order_by_proto_to_enum,
    to_proto_date,
    to_python_date,
)
from app.schemas.query import QueryParams
from app.services.query import QueryService


class OccupancyService:
    async def query(
        self, request: occupancy_pb2.QueryRequest
    ) -> occupancy_pb2.QueryResponse:
        try:
            query = self.__to_query(request)
            locale = grpc_language_to_locale(request.language)

            with with_locale(locale):
                return await self.__execute_query(query)

        except ValidationError as err:
            raise InvalidArgument(str(err)) from err

    async def query_reservation_sources(
        self, request: occupancy_pb2.QueryReservationSourcesRequest
    ) -> occupancy_pb2.QueryReservationSourcesResponse:
        try:
            query = self.__to_query_reservation_sources(request)
            locale = grpc_language_to_locale(request.language)

            with with_locale(locale):
                return await self.__execute_query_reservation_sources(query)

        except ValidationError as err:
            raise InvalidArgument(str(err)) from err

    async def __execute_query(
        self, query_params: QueryParams
    ) -> occupancy_pb2.QueryResponse:
        """
        Execute the query and return the result.
        :param query_params:
        :return:
        """
        async with get_session() as session:
            query_result = await QueryService(
                session,
                query_params.property_ids,
                query_params.organization_id,
            ).get(
                columns=query_params.columns,
                group_by=query_params.group_by if query_params.group_by else [],
                start_date=query_params.start_date,
                end_date=query_params.end_date,
                granularity=query_params.granularity,
                offset=query_params.offset,
                limit=query_params.limit,
                room_type_ids=query_params.room_type_ids
                if query_params.room_type_ids
                else [],
                sort=query_params.sort,
            )

            return self.convert_to_proto_response(query_result)

    async def __execute_query_reservation_sources(
        self, query_params: QueryParams
    ) -> occupancy_pb2.QueryReservationSourcesResponse:
        async with get_session() as session:
            query_service = QueryService(
                session,
                property_ids=query_params.property_ids,
                organization_id=query_params.organization_id,
            )

            data, limit, offset, _ = await query_service.get(
                columns=query_params.columns,
                group_by=query_params.group_by,
                start_date=query_params.start_date,
                end_date=query_params.end_date,
                granularity=query_params.granularity,
                offset=query_params.offset,
                limit=query_params.limit,
                room_type_ids=query_params.room_type_ids,
                sort=query_params.sort,
            )

            response = occupancy_pb2.QueryReservationSourcesResponse(
                limit=limit,
                offset=offset,
            )

            # data is Dict[str, Dict[str, Any]]
            for __reservation_source, metrics in data.items():
                reservation_source = (
                    occupancy_pb2.QueryReservationSourcesResponse.ReservationSource()
                )
                reservation_source.reservation_source = __reservation_source
                data = occupancy_pb2.Data()

                for column, metric in metrics.items():
                    self.__fill_data(column, metric, data)

                reservation_source.data.CopyFrom(data)
                response.reservation_sources.append(reservation_source)

            return response

    def convert_to_proto_response(
        self, query_result: tuple
    ) -> occupancy_pb2.QueryResponse:
        """
        Convert the query result to a protobuf response.
        :param query_result:
        :return:
        """
        data, limit, offset, sort = query_result
        logger.info(f"data={data}, limit={limit}, offset={offset}, sort={sort}")

        response = occupancy_pb2.QueryResponse()
        response.offset = offset
        response.limit = limit

        if len(data) == 0:
            return response

        # records is Dict[str, Dict[str, Any]] | List[Any]
        if type(data) == list:
            self.__create_list(data, response)
        elif type(data) == dict:
            self.__create_summary(data, response)
        else:
            raise ValueError(f"unknown type {type(data)}")

        return response

    def __create_list(self, records: List[Any], response: occupancy_pb2.QueryResponse):
        """
        Create a list response for non-grouped data.
        """
        list_of_data = occupancy_pb2.QueryResponse.ListOfData()

        for row in records:
            data = occupancy_pb2.Data()
            for column, value in row.items():
                try:
                    self.__fill_data(column, value, data)
                except Exception as e:
                    logger.exception(
                        f"error converting value: column={column} value={value}, type={type(value)}",
                        e,
                    )
            list_of_data.data.append(data)

        response.list.CopyFrom(list_of_data)

    def __create_summary(
        self, records: Dict[str, Dict[str, Any]], response: occupancy_pb2.QueryResponse
    ):
        """
        Create a summary response for grouped data.
        """
        summary = occupancy_pb2.QueryResponse.Summary()

        for key, value in records.items():
            summary.groups[key].CopyFrom(self.__create_grouped(value))

        response.summary.CopyFrom(summary)

    def __create_grouped(
        self, groups: Dict[str, Any]
    ) -> occupancy_pb2.QueryResponse.Grouped:
        """
        Create a grouped response for grouped data. This will recurse until we reach the leaf node with data.
        """
        grouped = occupancy_pb2.QueryResponse.Grouped()
        data = None
        summary = None
        for key, value in groups.items():
            # if value is dict, it is a sub group
            if type(value) == dict:
                if summary is None:
                    summary = occupancy_pb2.QueryResponse.Summary()
                summary.groups[key].CopyFrom(self.__create_grouped(value))
            else:
                if data is None:
                    data = occupancy_pb2.Data()
                self.__fill_data(key, value, data)

        if summary is not None:
            grouped.sub_groups.CopyFrom(summary)

        if data is not None:
            grouped.data.CopyFrom(data)

        return grouped

    def __fill_data(self, col: Column, value: any, data: occupancy_pb2.Data):
        """
        Fill the data object with the value for the given column.
        """
        dispatch_map = {
            "stay_date": lambda v: data.stay_date.CopyFrom(to_proto_date(v)),
            "organization_id": lambda v: setattr(
                data, "organization_id", int(v) if v is not None else 0
            ),
            "property_id": lambda v: setattr(
                data, "property_id", int(v) if v is not None else 0
            ),
            "room_type_id": lambda v: setattr(
                data, "room_type_id", int(v) if v is not None else 0
            ),
            "room_id": lambda v: setattr(
                data, "room_id", str(v) if v is not None else ""
            ),
            "room_type_short_title": lambda v: setattr(
                data, "room_type_short_title", str(v) if v is not None else ""
            ),
            "room_name": lambda v: setattr(
                data, "room_name", str(v) if v is not None else ""
            ),
            "room_type": lambda v: setattr(
                data, "room_type", str(v) if v is not None else ""
            ),
            "group_profile": lambda v: setattr(
                data, "group_profile", str(v) if v is not None else 0
            ),
            "group_profile_code": lambda v: setattr(
                data, "group_profile_code", str(v) if v is not None else 0
            ),
            "group_profile_id": lambda v: setattr(
                data, "group_profile_id", int(v) if v is not None else 0
            ),
            "total_room_rate": lambda v: setattr(
                data, "total_room_rate", float(v) if v is not None else 0.0
            ),
            "total_other_room_revenue": lambda v: setattr(
                data, "total_other_room_revenue", float(v) if v is not None else 0.0
            ),
            "total_room_revenue": lambda v: setattr(
                data, "total_room_revenue", float(v) if v is not None else 0.0
            ),
            "total_room_revenue_adjustments": lambda v: setattr(
                data,
                "total_room_revenue_adjustments",
                float(v) if v is not None else 0.0,
            ),
            "total_other_revenue": lambda v: setattr(
                data, "total_other_revenue", float(v) if v is not None else 0.0
            ),
            "total_revenue": lambda v: setattr(
                data, "total_revenue", float(v) if v is not None else 0.0
            ),
            "total_rooms_sold": lambda v: setattr(
                data, "total_rooms_sold", int(v) if v is not None else 0
            ),
            "total_rooms_available": lambda v: setattr(
                data, "total_rooms_available", int(v) if v is not None else 0
            ),
            "out_of_service_count": lambda v: setattr(
                data, "out_of_service_count", int(v) if v is not None else 0
            ),
            "blocked_rooms_count": lambda v: setattr(
                data, "blocked_rooms_count", int(v) if v is not None else 0
            ),
            "capacity_count": lambda v: setattr(
                data, "capacity_count", int(v) if v is not None else 0
            ),
            "adr": lambda v: setattr(data, "adr", float(v) if v is not None else 0.0),
            "revpar": lambda v: setattr(
                data, "revpar", float(v) if v is not None else 0.0
            ),
            "occupancy": lambda v: setattr(
                data, "occupancy", float(v) if v is not None else 0.0
            ),
            "total_room_taxes": lambda v: setattr(
                data, "total_room_taxes", float(v) if v is not None else 0.0
            ),
            "total_room_fees": lambda v: setattr(
                data, "total_room_fees", float(v) if v is not None else 0.0
            ),
            "guest_count": lambda v: setattr(
                data, "guest_count", int(v) if v is not None else 0
            ),
            "children_count": lambda v: setattr(
                data, "children_count", int(v) if v is not None else 0
            ),
            "adults_count": lambda v: setattr(
                data, "adults_count", int(v) if v is not None else 0
            ),
        }

        try:
            dispatch_map[col](value)
        except KeyError as key_exception:
            raise ValueError(
                f"Unknown column: {col}. Type: {type(col)}, Value: {value}, Value Type: {type(value)}"
            ) from key_exception
        except (ValueError, TypeError) as exception:
            raise ValueError(
                f"Failed to assign value to {col}: {exception}"
            ) from exception

    def __to_query(self, request: occupancy_pb2.QueryRequest) -> QueryParams:
        """
        Convert the protobuf request to a QueryParams object. This allows us to rely upon pydantic for
        validation.
        """
        return QueryParams(
            organization_id=request.organization_id,
            property_ids=request.property_ids if request.property_ids else [],
            start_date=to_python_date(request.start_date),
            end_date=to_python_date(request.end_date),
            granularity=granularity_proto_to_enum(request.granularity)
            if request.granularity
            else Granularity.day,
            columns=[
                column
                for column in [
                    column_proto_to_enum(proto_column)
                    for proto_column in request.columns
                ]
                if column is not None
            ],
            group_by=[
                group_by
                for group_by in [
                    group_by_proto_to_enum(proto_group_by)
                    for proto_group_by in request.group_by
                ]
                if group_by is not None
            ],
            room_type_ids=request.room_type_ids if request.room_type_ids else [],
            offset=request.offset,
            limit=request.limit if request.limit > 0 else DEFAULT_PAGINATION_LIMIT,
            sort=order_by_proto_to_enum(request.order_by),
        )

    def __to_query_reservation_sources(
        self, request: occupancy_pb2.QueryReservationSourcesRequest
    ) -> QueryParams:
        return QueryParams(
            organization_id=request.organization_id,
            property_ids=request.property_ids if request.property_ids else [],
            columns=[
                Column.total_room_rate,
                Column.total_room_revenue,
                Column.total_other_room_revenue,
                Column.total_room_revenue_adjustments,
                Column.total_room_taxes,
                Column.total_room_fees,
                Column.total_other_revenue,
                Column.total_revenue,
                Column.adr,
                Column.guest_count,
                Column.adults_count,
                Column.children_count,
            ],
            group_by=[GroupBy.reservation_source],
            start_date=to_python_date(request.start_date),
            end_date=to_python_date(request.end_date),
            granularity=granularity_proto_to_enum(request.granularity)
            if request.granularity
            else Granularity.day,
            room_type_ids=request.room_type_ids if request.room_type_ids else [],
            offset=request.offset,
            limit=request.limit if request.limit > 0 else DEFAULT_PAGINATION_LIMIT,
            sort=order_by_proto_to_enum(request.order_by),
        )
