import types
import uuid

import grpc
from grpc.aio import ServerInterceptor

import app.common.context_variables as global_vars
from app.common.logger import logger


class LoggerInterceptor(ServerInterceptor):
    async def intercept_service(self, continuation, handler_call_details):
        request_context = types.SimpleNamespace()
        request_context.request_id = str(uuid.uuid4())
        request_context.organization_id = None
        request_context.property_ids = None
        request_context.user_agent = None
        request_context.method = handler_call_details.method

        global_vars.request_global.set(request_context)

        handler = await continuation(handler_call_details)
        if handler is None:
            return None

        async def wrapper(request, context):
            g = global_vars.request_global.get()
            g.organization_id = getattr(request, "organization_id", None)
            g.property_ids = getattr(request, "property_ids", None)
            g.user_agent = context.peer()
            global_vars.request_global.set(g)

            try:
                response = await handler.unary_unary(request, context)
                logger.info(
                    f"gRPC request: {g.method}",
                    extra={
                        "method": g.method,
                        "request_id": g.request_id,
                        "organization_id": g.organization_id,
                        "property_ids": g.property_ids,
                        "user_agent": g.user_agent,
                    },
                )
                return response

            except Exception:
                logger.exception(
                    f"gRPC error in method: {g.method}",
                    extra={
                        "method": g.method,
                        "request_id": g.request_id,
                        "organization_id": g.organization_id,
                        "property_ids": g.property_ids,
                        "user_agent": g.user_agent,
                    },
                )
                raise

        if handler.unary_unary:
            return grpc.unary_unary_rpc_method_handler(
                wrapper,
                request_deserializer=handler.request_deserializer,
                response_serializer=handler.response_serializer,
            )

        return handler
