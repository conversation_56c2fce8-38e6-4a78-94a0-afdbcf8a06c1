import grpc
from cloudbeds.user.v1 import user_pb2, user_pb2_grpc
from google.protobuf.json_format import MessageToDict

from app.services.grpc.exception_handler import grpc_exception_handler
from app.settings import settings


class UserServiceClient:
    SERVICE_NAME = "User Service"

    def __init__(self):
        self._grpc_url = settings.USER_SERVICE_URL
        self._ssl = settings.USER_SERVICE_SSL
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = user_pb2_grpc.UserServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    @grpc_exception_handler(SERVICE_NAME)
    def get_user_by_id(self, user_id: int) -> user_pb2.GetUserResponse:
        return self._stub.GetUser(
            user_pb2.GetUserRequest(id=user_id),
            timeout=settings.GRPC_TIMEOUT,
        )

    @grpc_exception_handler(SERVICE_NAME)
    def get_assignment_by_user_id(
        self, user_id: int
    ) -> user_pb2.GetUserAssignmentResponse:
        return self._stub.GetUserAssignment(
            user_pb2.GetUserAssignmentRequest(id=user_id),
            timeout=settings.GRPC_TIMEOUT,
        )

    @grpc_exception_handler(SERVICE_NAME)
    def get_users_by_email(self, user_email: int):
        email_filter = {
            "field_filter": {
                "field": "FIELD_EMAIL",
                "operator": 5,
                "value": {"string_value": f"{user_email}"},
            }
        }

        response = MessageToDict(
            self._stub.ListUsers(
                user_pb2.ListUsersRequest(filter=email_filter),
                timeout=settings.GRPC_TIMEOUT,
            ),
            preserving_proto_field_name=True,
        )
        return response
