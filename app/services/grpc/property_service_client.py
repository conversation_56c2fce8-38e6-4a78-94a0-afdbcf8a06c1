import grpc
from cloudbeds.organization.v1 import property_pb2, property_pb2_grpc

from app.services.grpc.exception_handler import grpc_exception_handler
from app.settings import settings


class PropertyServiceClient:
    SERVICE_NAME = "Property Service"

    def __init__(self):
        self._grpc_url = settings.ORGANIZATION_SERVICE_URL
        self._ssl = settings.ORGANIZATION_SERVICE_SSL
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = property_pb2_grpc.PropertyServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    @grpc_exception_handler(SERVICE_NAME)
    def get_properties_by_ids(
        self, property_ids: list[str | int]
    ) -> property_pb2.ListPropertiesResponse:
        return self._stub.ListProperties(
            property_pb2.ListPropertiesRequest(
                ids=[int(property_id) for property_id in property_ids],
            ),
            timeout=settings.GRPC_TIMEOUT,
        )
