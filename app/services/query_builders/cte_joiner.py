"""
CTE join logic for optimized queries.
"""

from typing import Any, Dict

from sqlalchemy import and_


class CTEJoiner:
    """Builds join conditions between CTEs."""

    @staticmethod
    def get_join_conditions(s, cap, grouping_columns: Dict[str, Any]):
        """Get join conditions between capacity and sold CTEs based on grouping columns."""
        join_conditions = []

        for column_key in grouping_columns.keys():
            if hasattr(cap.c, column_key) and hasattr(s.c, column_key):
                join_conditions.append(
                    getattr(cap.c, column_key) == getattr(s.c, column_key)
                )

        return and_(*join_conditions) if join_conditions else True
