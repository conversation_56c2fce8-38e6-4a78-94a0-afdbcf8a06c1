"""
Assignments CTE for optimized queries.
"""

from datetime import date
from typing import Any, Dict, List

from sqlalchemy import and_, func, select

from app.models.occupancy import OccupancyAssignmentsGreen


class CTEAssignments:
    """Builds assignments aggregation CTE from occupancy_assignments_green."""

    def __init__(self, property_ids: List[int], organization_id: int):
        self.property_ids = property_ids
        self.organization_id = organization_id

    def build(
        self,
        start_date: date,
        end_date: date,
        room_type_ids: List[int] = None,
        grouping_columns: Dict[str, Any] = None,
    ):
        """Build assignments aggregation CTE with dynamic grouping."""
        if grouping_columns is None:
            # Default grouping for backward compatibility
            grouping_columns = {
                "organization_id": OccupancyAssignmentsGreen.organization_id,
                "property_id": OccupancyAssignmentsGreen.property_id,
                "room_type_id": OccupancyAssignmentsGreen.room_type_id,
                "stay_date": OccupancyAssignmentsGreen.stay_date,
            }
        else:
            # Map grouping columns to assignments table
            # Note: assignments table doesn't have room_type or group_profile_code columns
            mapped_grouping = {}
            for key, _ in grouping_columns.items():
                if key == "organization_id":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.organization_id
                elif key == "property_id":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.property_id
                elif key == "room_type_id":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.room_type_id
                elif key == "stay_date":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.stay_date
                elif key == "group_profile_id":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.group_profile_id
                elif key == "group_profile_code":
                    mapped_grouping[key] = OccupancyAssignmentsGreen.group_profile_code
                # Skip room_type as we want it to come from capacity CTE only
                elif key == "room_type":
                    continue
            grouping_columns = mapped_grouping

        filters = [
            OccupancyAssignmentsGreen.organization_id == self.organization_id,
            OccupancyAssignmentsGreen.property_id.in_(self.property_ids),
            OccupancyAssignmentsGreen.stay_date >= start_date,
            OccupancyAssignmentsGreen.stay_date <= end_date,
            OccupancyAssignmentsGreen.accomodation_kind == "Physical",
        ]

        if room_type_ids:
            filters.append(OccupancyAssignmentsGreen.room_type_id.in_(room_type_ids))

        # Build select columns dynamically based on grouping
        select_columns = list(grouping_columns.values()) + [
            func.sum(OccupancyAssignmentsGreen.room_sold).label("total_sold"),
            func.sum(OccupancyAssignmentsGreen.out_of_service).label(
                "total_out_of_service"
            ),
            func.sum(OccupancyAssignmentsGreen.blocked_room).label("total_blocked"),
            func.sum(OccupancyAssignmentsGreen.room_adults_count).label("total_adults"),
            func.sum(OccupancyAssignmentsGreen.room_children_count).label(
                "total_children"
            ),
            func.sum(OccupancyAssignmentsGreen.room_guest_count).label("total_guests"),
        ]

        assignments_cte = (
            select(*select_columns)
            .where(and_(*filters))
            .group_by(*grouping_columns.values())
        ).cte("assignments_aggregated")

        return assignments_cte
