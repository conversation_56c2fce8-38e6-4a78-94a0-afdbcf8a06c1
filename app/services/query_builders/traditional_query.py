"""
Traditional query builder for legacy SQL approach.
"""

from datetime import date
from typing import Any, List

from sqlalchemy import and_
from sqlalchemy.sql import select


class TraditionalQuery:
    """Builds traditional queries using the main occupancy tables."""

    def __init__(self, property_ids: List[int], organization_id: int, model: type):
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.model = model

    def build(
        self,
        selected_columns: List[Any],
        group_by_expressions: List[Any],
        start_date: date,
        end_date: date,
        room_type_ids: List[int] = None,
        sort_columns: List[Any] = None,
    ) -> select:
        """Build traditional query using the main occupancy tables."""
        filters = self._get_filters(start_date, end_date, room_type_ids)
        query = select(*selected_columns).where(filters).group_by(*group_by_expressions)

        if sort_columns:
            query = query.order_by(*sort_columns)
        else:
            query = query.order_by(*group_by_expressions)

        return query

    def _get_filters(
        self, start_date: date, end_date: date, room_type_ids: List[int] = None
    ):
        """Construct filter conditions for the query."""
        clauses = [
            self.model.stay_date >= start_date,
            self.model.stay_date <= end_date,
            self.model.organization_id == self.organization_id,
            self.model.property_id.in_(self.property_ids),
        ]

        if room_type_ids:
            clauses.append(self.model.room_type_id.in_(room_type_ids))

        return and_(*clauses)
