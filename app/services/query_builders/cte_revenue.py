"""
Revenue CTE for optimized queries.
"""

from datetime import date
from typing import Any, Dict, List

from sqlalchemy import and_, func, select

from app.models.occupancy import (
    FinancialSummaryBlue,
    OccupancyAssignmentsGreen,
    OccupancyRoomsGreen,
)


class CTERevenue:
    """Builds revenue aggregation CTE from assignments, rooms, and financial tables."""

    def __init__(self, property_ids: List[int], organization_id: int):
        self.property_ids = property_ids
        self.organization_id = organization_id

    def build(
        self,
        start_date: date,
        end_date: date,
        room_type_ids: List[int] = None,
        grouping_columns: Dict[str, Any] = None,
    ):
        """Build revenue aggregation CTE with dynamic grouping."""
        if grouping_columns is None:
            # Default grouping for backward compatibility
            grouping_columns = {
                "organization_id": OccupancyAssignmentsGreen.organization_id,
                "property_id": OccupancyAssignmentsGreen.property_id,
                "room_type_id": OccupancyAssignmentsGreen.room_type_id,
                "stay_date": OccupancyAssignmentsGreen.stay_date,
            }

        # Build filters for assignments table
        assignment_filters = [
            OccupancyAssignmentsGreen.organization_id == self.organization_id,
            OccupancyAssignmentsGreen.property_id.in_(self.property_ids),
            OccupancyAssignmentsGreen.stay_date >= start_date,
            OccupancyAssignmentsGreen.stay_date <= end_date,
            OccupancyAssignmentsGreen.accomodation_kind == "Physical",
        ]

        # Build filters for rooms table
        room_filters = [
            OccupancyRoomsGreen.organization_id == self.organization_id,
            OccupancyRoomsGreen.property_id.in_(self.property_ids),
            OccupancyRoomsGreen.stay_date >= start_date,
            OccupancyRoomsGreen.stay_date <= end_date,
            OccupancyRoomsGreen.accomodation_kind == "Physical",
        ]

        # Build filters for financial table
        financial_filters = [
            FinancialSummaryBlue.organization_id == self.organization_id,
            FinancialSummaryBlue.property_id.in_(self.property_ids),
            FinancialSummaryBlue.service_date >= start_date,
            FinancialSummaryBlue.service_date <= end_date,
        ]

        if room_type_ids:
            assignment_filters.append(
                OccupancyAssignmentsGreen.room_type_id.in_(room_type_ids)
            )
            room_filters.append(OccupancyRoomsGreen.room_type_id.in_(room_type_ids))

        # Map grouping columns to use assignments table as base
        mapped_grouping = {}
        for key, _value in grouping_columns.items():
            if key == "room_type":
                # Use room_type from rooms table (COALESCE with assignments.room_type)
                mapped_grouping[key] = func.coalesce(
                    OccupancyAssignmentsGreen.room_type, OccupancyRoomsGreen.room_type
                ).label("room_type")
            elif key == "group_profile_code":
                # Use group_profile_code from rooms table (COALESCE with assignments.group_profile_code)
                mapped_grouping[key] = func.coalesce(
                    OccupancyAssignmentsGreen.group_profile_code,
                    OccupancyRoomsGreen.group_profile_code,
                ).label("group_profile_code")
            elif key == "group_profile_id":
                # Use group_profile_id from assignments table (should be consistent)
                mapped_grouping[key] = OccupancyAssignmentsGreen.group_profile_id
            else:
                # Map to assignments table
                mapped_grouping[key] = getattr(OccupancyAssignmentsGreen, key)

        # Build select columns dynamically based on grouping
        select_columns = list(mapped_grouping.values()) + [
            func.sum(OccupancyAssignmentsGreen.room_sold).label("total_sold"),
            func.sum(OccupancyAssignmentsGreen.room_guest_count).label("total_guests"),
            # All financial columns
            func.sum(FinancialSummaryBlue.total_room_rate).label("total_room_rate"),
            func.sum(FinancialSummaryBlue.total_other_room_revenue).label(
                "total_other_room_revenue"
            ),
            func.sum(FinancialSummaryBlue.total_room_revenue_adjustments).label(
                "total_room_revenue_adjustments"
            ),
            func.sum(FinancialSummaryBlue.room_revenue).label("total_room_revenue"),
            func.sum(FinancialSummaryBlue.total_room_taxes).label("total_room_taxes"),
            func.sum(FinancialSummaryBlue.total_room_fees).label("total_room_fees"),
            func.sum(FinancialSummaryBlue.total_other_revenue).label(
                "total_other_revenue"
            ),
            func.sum(FinancialSummaryBlue.total_revenue).label("total_revenue"),
        ]

        # Build the complex join query as specified in the ticket
        revenue_cte = (
            select(*select_columns)
            .select_from(
                OccupancyAssignmentsGreen.__table__.join(
                    OccupancyRoomsGreen.__table__,
                    and_(
                        OccupancyAssignmentsGreen.organization_id
                        == OccupancyRoomsGreen.organization_id,
                        OccupancyAssignmentsGreen.property_id
                        == OccupancyRoomsGreen.property_id,
                        OccupancyAssignmentsGreen.room_type_id
                        == OccupancyRoomsGreen.room_type_id,
                        OccupancyAssignmentsGreen.room_id
                        == OccupancyRoomsGreen.room_id,
                        OccupancyAssignmentsGreen.stay_date
                        == OccupancyRoomsGreen.stay_date,
                    ),
                ).join(
                    FinancialSummaryBlue.__table__,
                    and_(
                        OccupancyAssignmentsGreen.organization_id
                        == FinancialSummaryBlue.organization_id,
                        OccupancyAssignmentsGreen.property_id
                        == FinancialSummaryBlue.property_id,
                        OccupancyAssignmentsGreen.booking_room_id
                        == FinancialSummaryBlue.booking_rooms_id,
                        OccupancyAssignmentsGreen.stay_date
                        == FinancialSummaryBlue.service_date,
                        OccupancyRoomsGreen.organization_id
                        == FinancialSummaryBlue.organization_id,
                        OccupancyRoomsGreen.property_id
                        == FinancialSummaryBlue.property_id,
                        OccupancyRoomsGreen.stay_date
                        == FinancialSummaryBlue.service_date,
                    ),
                )
            )
            .where(
                and_(
                    *assignment_filters,
                    *room_filters,
                    *financial_filters,
                )
            )
            .group_by(*mapped_grouping.values())
        ).cte("revenue_aggregated")

        return revenue_cte
