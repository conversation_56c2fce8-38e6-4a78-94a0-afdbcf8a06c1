"""
Optimized query builder using CTEs.
"""

from datetime import date
from typing import Any, List

from sqlalchemy.sql import select

from .column_mapper import ColumnMapper
from .cte_assignments import CTEAssignments
from .cte_capacity import CTECapacity
from .cte_grouping import CTEGrouping
from .cte_joiner import <PERSON><PERSON><PERSON><PERSON>ner


class OptimizedQuery:
    """Builds optimized CTE-based queries using green tables."""

    def __init__(self, property_ids: List[int], organization_id: int):
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.capacity_cte = CTECapacity(property_ids, organization_id)
        self.assignments_cte = CTEAssignments(property_ids, organization_id)
        self.grouping = CTEGrouping()
        self.mapper = ColumnMapper()
        self.joiner = CTEJoiner()

    def build(
        self,
        selected_columns: List[Any],
        group_by_expressions: List[Any],
        start_date: date,
        end_date: date,
        room_type_ids: List[int] = None,
        sort_columns: List[Any] = None,
    ) -> select:
        """Build CTE-based query using green tables for optimized occupancy-only queries."""
        # Determine grouping columns based on group_by_expressions
        grouping_columns = self.grouping.determine_columns(group_by_expressions)

        # Build capacity CTE with dynamic grouping
        capacity_cte = self.capacity_cte.build(
            start_date, end_date, room_type_ids, grouping_columns
        )

        # Build assignments CTE with dynamic grouping
        assignments_cte = self.assignments_cte.build(
            start_date, end_date, room_type_ids, grouping_columns
        )

        # Alias the CTEs for easier reference
        s = assignments_cte.alias("s")
        cap = capacity_cte.alias("cap")

        # Map selected columns to use CTE aliases
        mapped_columns, mapped_group_by = self.mapper.map_columns_and_group_by(
            selected_columns, group_by_expressions, s, cap
        )

        # Get join conditions
        join_conditions = self.joiner.get_join_conditions(s, cap, grouping_columns)

        query = (
            select(*mapped_columns)
            .select_from(cap.join(s, onclause=join_conditions, isouter=True))
            .group_by(*mapped_group_by)
        )

        # Skip ORDER BY for optimized queries for now to avoid column reference issues
        # TODO: Implement proper ORDER BY mapping for CTE-based queries

        return query
