"""
CTE grouping logic for optimized queries.
"""

from typing import Any, Dict, List

from app.models.occupancy import Occupancy<PERSON>oomsGreen


class CTEGrouping:
    """Determines grouping columns for CTEs based on query requirements."""

    @staticmethod
    def determine_columns(group_by_expressions: List[Any]) -> Dict[str, Any]:
        """
        Determine which columns to group by in the CTEs based on the main query group by.
        Following <PERSON>'s requirements:
        - If group_profile_code: disallow other group bys, use group profile CTE
        - Otherwise: use room_type CTE with room_type_id
        """
        grouping_columns = {
            "organization_id": OccupancyRoomsGreen.organization_id,
            "property_id": OccupancyRoomsGreen.property_id,
            "stay_date": OccupancyRoomsGreen.stay_date,
        }

        # Check if we're grouping by group_profile_code (exclusive mode)
        has_group_profile = False
        has_room_type = False

        for expr in group_by_expressions:
            if hasattr(expr, "name"):
                if expr.name == "group_profile_code":
                    has_group_profile = True
                elif expr.name == "room_type":
                    has_room_type = True

        if has_group_profile:
            # Group profile mode: use group_profile_id, disallow other dimensions
            grouping_columns["group_profile_id"] = OccupancyRoomsGreen.group_profile_id
            grouping_columns[
                "group_profile_code"
            ] = OccupancyRoomsGreen.group_profile_code
        else:
            # Room type mode: always include room_type_id
            grouping_columns["room_type_id"] = OccupancyRoomsGreen.room_type_id
            if has_room_type:
                # Include room_type column when grouping by room_type
                grouping_columns["room_type"] = OccupancyRoomsGreen.room_type

        return grouping_columns
