"""
Capacity CTE for optimized queries.
"""

from datetime import date
from typing import Any, Dict, List

from sqlalchemy import and_, func, select

from app.models.occupancy import OccupancyRoomsGreen


class CTECapacity:
    """Builds capacity aggregation CTE from occupancy_rooms_green."""

    def __init__(self, property_ids: List[int], organization_id: int):
        self.property_ids = property_ids
        self.organization_id = organization_id

    def build(
        self,
        start_date: date,
        end_date: date,
        room_type_ids: List[int] = None,
        grouping_columns: Dict[str, Any] = None,
    ):
        """Build capacity aggregation CTE with dynamic grouping."""
        if grouping_columns is None:
            # Default grouping for backward compatibility
            grouping_columns = {
                "organization_id": OccupancyRoomsGreen.organization_id,
                "property_id": OccupancyRoomsGreen.property_id,
                "room_type_id": OccupancyRoomsGreen.room_type_id,
                "stay_date": OccupancyRoomsGreen.stay_date,
            }

        filters = [
            OccupancyRoomsGreen.organization_id == self.organization_id,
            OccupancyRoomsGreen.property_id.in_(self.property_ids),
            OccupancyRoomsGreen.stay_date >= start_date,
            OccupancyRoomsGreen.stay_date <= end_date,
            OccupancyRoomsGreen.accomodation_kind == "Physical",
        ]

        if room_type_ids:
            filters.append(OccupancyRoomsGreen.room_type_id.in_(room_type_ids))

        # Build select columns dynamically based on grouping
        select_columns = list(grouping_columns.values()) + [
            func.sum(OccupancyRoomsGreen.capacity_count).label("total_capacity"),
            func.sum(OccupancyRoomsGreen.room_available).label("total_available"),
            func.sum(OccupancyRoomsGreen.out_of_service).label(
                "total_out_of_service_rooms"
            ),
            func.sum(OccupancyRoomsGreen.blocked_room).label("total_blocked_rooms"),
        ]

        capacity_cte = (
            select(*select_columns)
            .where(and_(*filters))
            .group_by(*grouping_columns.values())
        ).cte("capacity_aggregated")

        return capacity_cte
