"""
Column mapping for optimized CTE queries.
"""

from typing import Any, List, Tuple

from sqlalchemy import Float, Integer, cast, func


class ColumnMapper:
    """Maps columns and GROUP BY expressions for CTE-based queries."""

    @staticmethod
    def map_columns_and_group_by(
        selected_columns: List[Any], group_by_expressions: List[Any], s, cap, rev=None
    ) -> Tuple[List[Any], List[Any]]:
        """Map selected columns and group_by to work with CTE-based optimized query."""
        mapped_columns = []
        group_by_names = [
            expr.name for expr in group_by_expressions if hasattr(expr, "name")
        ]

        """Map group by expressions for optimized query."""
        mapped_group_by = []
        for expr in group_by_expressions:
            if hasattr(expr, "name"):
                if expr.name == "stay_date":
                    # For stay_date, we only need to group by the formatted version
                    if rev is not None:
                        fn = func.to_char(rev.c.stay_date, "YYYY-MM-DD")
                    else:
                        fn = func.to_char(cap.c.stay_date, "YYYY-MM-DD")
                    mapped_columns.append(fn)
                    mapped_group_by.append(fn)
                elif expr.name == "room_type":
                    # room_type comes from the appropriate CTE
                    if rev is not None and hasattr(rev.c, "room_type"):
                        mapped_columns.append(rev.c.room_type)
                        mapped_group_by.append(rev.c.room_type)
                    elif cap is not None and hasattr(cap.c, "room_type"):
                        mapped_columns.append(cap.c.room_type)
                        mapped_group_by.append(cap.c.room_type)
                elif expr.name == "group_profile_code":
                    # group_profile_code can come from any CTE
                    if rev is not None and hasattr(rev.c, "group_profile_code"):
                        mapped_columns.append(rev.c.group_profile_code)
                        mapped_group_by.append(rev.c.group_profile_code)
                    elif cap is not None and hasattr(cap.c, "group_profile_code"):
                        mapped_columns.append(cap.c.group_profile_code)
                        mapped_group_by.append(cap.c.group_profile_code)
                    elif s is not None and hasattr(s.c, "group_profile_code"):
                        mapped_columns.append(s.c.group_profile_code)
                        mapped_group_by.append(s.c.group_profile_code)
            else:
                mapped_group_by.append(expr)

        for column in selected_columns:
            if hasattr(column, "name"):
                # Skip columns that are already in group_by, these have been handled above.
                if column.name in group_by_names:
                    continue

                if column.name == "stay_date":
                    # Handle date formatting for stay_date
                    if rev is not None:
                        mapped_columns.append(
                            func.to_char(rev.c.stay_date, "YYYY-MM-DD").label("stay_date")
                        )
                    else:
                        mapped_columns.append(
                            func.to_char(cap.c.stay_date, "YYYY-MM-DD").label("stay_date")
                        )
                elif column.name == "occupancy":
                    # Handle occupancy calculation
                    if rev is not None:
                        # For revenue CTE, we need to calculate occupancy differently
                        # Since we don't have capacity in revenue CTE, use total_sold as proxy
                        mapped_columns.append(
                            cast(
                                func.coalesce(func.sum(rev.c.total_sold), 0),
                                Float,
                            ).label("occupancy")
                        )
                    else:
                        mapped_columns.append(
                            cast(
                                func.coalesce(
                                    (
                                        func.sum(s.c.total_sold)
                                        / func.nullif(func.sum(cap.c.total_capacity), 0)
                                    )
                                    * 100,
                                    0,
                                ),
                                Float,
                            ).label("occupancy")
                        )
                elif column.name == "capacity_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(cap.c.total_capacity), 0), Integer
                        ).label("capacity_count")
                    )
                elif column.name == "total_rooms_sold":
                    if rev is not None:
                        mapped_columns.append(
                            cast(func.coalesce(func.sum(rev.c.total_sold), 0), Integer).label(
                                "total_rooms_sold"
                            )
                        )
                    else:
                        mapped_columns.append(
                            cast(func.coalesce(func.sum(s.c.total_sold), 0), Integer).label(
                                "total_rooms_sold"
                            )
                        )
                elif column.name == "total_rooms_available":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(cap.c.total_capacity), 0), Integer
                        ).label("total_rooms_available")
                    )
                elif column.name == "blocked_rooms_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_blocked), 0), Integer
                        ).label("blocked_rooms_count")
                    )
                elif column.name == "out_of_service_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_out_of_service), 0),
                            Integer,
                        ).label("out_of_service_count")
                    )
                elif column.name == "guest_count":
                    if rev is not None:
                        mapped_columns.append(
                            cast(
                                func.coalesce(func.sum(rev.c.total_guests), 0), Integer
                            ).label("guest_count")
                        )
                    else:
                        mapped_columns.append(
                            cast(
                                func.coalesce(func.sum(s.c.total_guests), 0), Integer
                            ).label("guest_count")
                        )
                elif column.name == "adults_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_adults), 0), Integer
                        ).label("adults_count")
                    )
                elif column.name == "children_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_children), 0), Integer
                        ).label("children_count")
                    )
                elif column.name == "room_type":
                    # Handle room_type grouping
                    if rev is not None and hasattr(rev.c, "room_type"):
                        mapped_columns.append(rev.c.room_type.label("room_type"))
                    elif cap is not None and hasattr(cap.c, "room_type"):
                        mapped_columns.append(cap.c.room_type.label("room_type"))
                    else:
                        mapped_columns.append(column)
                elif column.name == "group_profile_code":
                    # Handle group_profile_code grouping - can come from either CTE
                    if hasattr(cap.c, "group_profile_code"):
                        mapped_columns.append(
                            cap.c.group_profile_code.label("group_profile_code")
                        )
                    elif hasattr(s.c, "group_profile_code"):
                        mapped_columns.append(
                            s.c.group_profile_code.label("group_profile_code")
                        )
                    else:
                        mapped_columns.append(column)
                elif column.name == "total_room_revenue" and rev is not None:
                    # Handle total room revenue from revenue CTE
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(rev.c.total_room_revenue), 0), Float
                        ).label("total_room_revenue")
                    )
                elif column.name == "adr" and rev is not None:
                    # Handle ADR calculation from revenue CTE
                    mapped_columns.append(
                        cast(
                            func.coalesce(
                                func.sum(rev.c.total_room_revenue)
                                / func.nullif(func.sum(rev.c.total_sold), 0),
                                0,
                            ),
                            Float,
                        ).label("adr")
                    )
                elif column.name == "revpar" and rev is not None:
                    # Handle RevPAR calculation - need capacity from capacity CTE
                    # For now, use total_sold as proxy for capacity in revenue CTE
                    mapped_columns.append(
                        cast(
                            func.coalesce(
                                func.sum(rev.c.total_room_revenue)
                                / func.nullif(func.sum(rev.c.total_sold), 0),
                                0,
                            ),
                            Float,
                        ).label("revpar")
                    )
                else:
                    mapped_columns.append(column)
            else:
                mapped_columns.append(column)
        return mapped_columns, mapped_group_by
