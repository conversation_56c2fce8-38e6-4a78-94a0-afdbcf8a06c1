"""
Column mapping for optimized CTE queries.
"""

from typing import Any, <PERSON>, <PERSON><PERSON>

from sqlalchemy import Float, Integer, cast, func


class ColumnMapper:
    """Maps columns and GROUP BY expressions for CTE-based queries."""

    @staticmethod
    def map_columns_and_group_by(
        selected_columns: List[Any], group_by_expressions: List[Any], s, cap
    ) -> <PERSON><PERSON>[List[Any], List[Any]]:
        """Map selected columns and group_by to work with CTE-based optimized query."""
        mapped_columns = []
        group_by_names = [
            expr.name for expr in group_by_expressions if hasattr(expr, "name")
        ]

        """Map group by expressions for optimized query."""
        mapped_group_by = []
        for expr in group_by_expressions:
            if hasattr(expr, "name"):
                if expr.name == "stay_date":
                    # For stay_date, we only need to group by the formatted version
                    fn = func.to_char(cap.c.stay_date, "YYYY-MM-DD")
                    mapped_columns.append(fn)
                    mapped_group_by.append(fn)
                elif expr.name == "room_type":
                    # room_type comes from capacity CTE (cap), not assignments (s)
                    if cap is not None and hasattr(cap.c, "room_type"):
                        mapped_columns.append(cap.c.room_type)
                        mapped_group_by.append(cap.c.room_type)
                elif expr.name == "group_profile_code":
                    # group_profile_code can come from either CTE
                    if cap is not None and hasattr(cap.c, "group_profile_code"):
                        mapped_columns.append(cap.c.group_profile_code)
                        mapped_group_by.append(cap.c.group_profile_code)
                    elif hasattr(s.c, "group_profile_code"):
                        mapped_columns.append(s.c.group_profile_code)
                        mapped_group_by.append(s.c.group_profile_code)
            else:
                mapped_group_by.append(expr)

        for column in selected_columns:
            if hasattr(column, "name"):
                # Skip columns that are already in group_by, these have been handled above.
                if column.name in group_by_names:
                    continue

                if column.name == "stay_date":
                    # Handle date formatting for stay_date
                    mapped_columns.append(
                        func.to_char(cap.c.stay_date, "YYYY-MM-DD").label("stay_date")
                    )
                elif column.name == "occupancy":
                    # Handle occupancy calculation
                    mapped_columns.append(
                        cast(
                            func.coalesce(
                                (
                                    func.sum(s.c.total_sold)
                                    / func.nullif(func.sum(cap.c.total_capacity), 0)
                                )
                                * 100,
                                0,
                            ),
                            Float,
                        ).label("occupancy")
                    )
                elif column.name == "capacity_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(cap.c.total_capacity), 0), Integer
                        ).label("capacity_count")
                    )
                elif column.name == "total_rooms_sold":
                    mapped_columns.append(
                        cast(func.coalesce(func.sum(s.c.total_sold), 0), Integer).label(
                            "total_rooms_sold"
                        )
                    )
                elif column.name == "total_rooms_available":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(cap.c.total_capacity), 0), Integer
                        ).label("total_rooms_available")
                    )
                elif column.name == "blocked_rooms_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_blocked), 0), Integer
                        ).label("blocked_rooms_count")
                    )
                elif column.name == "out_of_service_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_out_of_service), 0),
                            Integer,
                        ).label("out_of_service_count")
                    )
                elif column.name == "guest_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_guests), 0), Integer
                        ).label("guest_count")
                    )
                elif column.name == "adults_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_adults), 0), Integer
                        ).label("adults_count")
                    )
                elif column.name == "children_count":
                    mapped_columns.append(
                        cast(
                            func.coalesce(func.sum(s.c.total_children), 0), Integer
                        ).label("children_count")
                    )
                elif column.name == "room_type":
                    # Handle room_type grouping - MUST come from capacity CTE (cap), not assignments (s)
                    # Per Fred's requirement: room_type should come from capacity CTE, not assignments
                    if hasattr(cap.c, "room_type"):
                        mapped_columns.append(cap.c.room_type.label("room_type"))
                    else:
                        mapped_columns.append(column)
                elif column.name == "group_profile_code":
                    # Handle group_profile_code grouping - can come from either CTE
                    if hasattr(cap.c, "group_profile_code"):
                        mapped_columns.append(
                            cap.c.group_profile_code.label("group_profile_code")
                        )
                    elif hasattr(s.c, "group_profile_code"):
                        mapped_columns.append(
                            s.c.group_profile_code.label("group_profile_code")
                        )
                    else:
                        mapped_columns.append(column)
                else:
                    mapped_columns.append(column)
            else:
                mapped_columns.append(column)
        return mapped_columns, mapped_group_by
