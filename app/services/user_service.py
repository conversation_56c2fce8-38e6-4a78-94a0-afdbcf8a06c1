from typing import List

from app.common.logger import logger
from app.services.grpc import PropertyServiceClient, UserServiceClient


class UserService:
    @staticmethod
    # @cache.memoize(TIMEOUT_HOUR)
    def get_user_assignment(user_id: int) -> tuple[List[int], List[int]]:
        """Method that will return a list of property IDs and organization IDs assigned to the user

           This method will first fetch the assignment from user service. If this user does not belong to a multi,
           property organization, then we will invoke property service to figure out the organization ids they also
           belong to.

        Args:
            user_id (int): User id to get the property id from
        """
        with UserServiceClient() as client:
            response = client.get_assignment_by_user_id(user_id=user_id)

            if not response or not response.assignment:
                logger.debug(f"No properties found for user: {user_id}")
                return [], []

            assignment = response.assignment
            if not assignment.properties and not assignment.organizations:
                logger.debug(
                    f"No properties or organizations found for user: {user_id}"
                )
                return [], []

            property_ids = []
            organization_ids = []

            if len(assignment.organizations) > 0:
                for organization in assignment.organizations:
                    organization_ids.append(organization.id)
                    for property in organization.properties:
                        property_ids.append(property.id)

                return property_ids, organization_ids

            for property in assignment.properties:
                property_ids.append(property.id)

            response = PropertyServiceClient().get_properties_by_ids(property_ids)
            if not response:
                logger.debug(
                    f"unable to determine organization id for user: {user_id}, property_ids: {property_ids}"
                )
                return property_ids, []

            for property in response.properties:
                organization_ids.append(property.organization.id)
            return property_ids, organization_ids
