import traceback

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.cache import cache
from app.common.logger import logger


class HealthService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def database_available(self) -> bool:
        try:
            await self.db.execute(text("SELECT 1"))
            return True
        except Exception as rds_exception:
            logger.error(
                f"Database Failed: {rds_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False

    async def redis_available(self) -> bool:
        try:
            await cache.cache.ping()
            return True
        except Exception as redis_exception:
            logger.error(
                f"Redis Failed: {redis_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False
