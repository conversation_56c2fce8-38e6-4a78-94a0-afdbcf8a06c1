from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.dependencies import authorization
from app.database.session import get_session
from app.schemas.query import QueryParams, QueryResponseSchema
from app.services.query import QueryService

router = APIRouter(
    prefix="/query",
    tags=["query"],
    responses={404: {"description": "Not found"}},
)


@router.get(
    "",
    response_model=QueryResponseSchema,
    responses={
        200: {
            "description": "Occupancy data successfully retrieved",
            "content": {
                "application/json": {
                    "examples": {
                        "grouped": {
                            "summary": "An example of a grouped response (group_by is requested)",
                            "value": {
                                "records": {
                                    "2024-05-01": {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Single room",
                                    },
                                    "2024-05-02": {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Single room",
                                    },
                                },
                                "columns": [
                                    "organization_id",
                                    "property_id",
                                    "occupancy",
                                    "room_type",
                                ],
                                "groups": ["stay_date"],
                                "limit": 100,
                                "offset": 0,
                            },
                        },
                        "list": {
                            "summary": "An example of a list response (group_by is not specified)",
                            "value": {
                                "records": [
                                    {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Anaconda Range 1 - 1 Bed of 2 Twin Bunks",
                                        "stay_date": "2024-05-01",
                                    },
                                    {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Anaconda Range 1 - 1 Bed of 2 Twin Bunks",
                                        "stay_date": "2024-05-02",
                                    },
                                    {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Automation accommodation",
                                        "stay_date": "2024-05-01",
                                    },
                                    {
                                        "organization_id": 177159,
                                        "property_id": 22425,
                                        "occupancy": 0.0,
                                        "room_type": "Automation accommodation",
                                        "stay_date": "2024-05-02",
                                    },
                                ],
                                "columns": [
                                    "organization_id",
                                    "property_id",
                                    "occupancy",
                                    "room_type",
                                    "stay_date",
                                ],
                                "groups": [],
                                "limit": 100,
                                "offset": 0,
                            },
                        },
                    }
                }
            },
        },
    },
)
async def get(
    query_params: QueryParams = Depends(),
    _: dict = Depends(authorization),
    db: AsyncSession = Depends(get_session),
) -> QueryResponseSchema:
    async with db as session:
        data, limit, offset, sort = await QueryService(
            session,
            query_params.property_ids,
            query_params.organization_id,
            cast_results_to_string=True,
        ).get(
            columns=query_params.columns,
            group_by=query_params.group_by if query_params.group_by else [],
            start_date=query_params.start_date,
            end_date=query_params.end_date,
            granularity=query_params.granularity,
            offset=query_params.offset,
            limit=query_params.limit,
            room_type_ids=query_params.room_type_ids,
            sort=query_params.sort if query_params.sort else [],
        )

        return QueryResponseSchema(data=data, limit=limit, offset=offset, sort=sort)
