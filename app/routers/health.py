from fastapi import APIRouter, Depends, Response
from fastapi import status as http_status_code
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_session
from app.schemas.health import HealthCheck
from app.services.health import HealthService

router = APIRouter(
    prefix="/health",
    tags=["healthcheck"],
    responses={404: {"description": "Not found"}},
)


@router.get(
    "",
    tags=["healthcheck"],
    summary="Perform a Health Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=http_status_code.HTTP_200_OK,
    response_model=HealthCheck,
)
async def get_health(
    response: Response, db: AsyncSession = Depends(get_session)
) -> HealthCheck:
    """
    ## Perform a Health Check
    Endpoint to perform a healthcheck on. This endpoint can primarily be used Docker
    to ensure a robust container orchestration and management is in place. Other
    services which rely on proper functioning of the API service will not deploy if this
    endpoint returns any other HTTP status code except 200 (OK).
    Returns:
        HealthCheck: Returns a JSON response with the health status
    """
    async with db as session:
        health_service = HealthService(session)
        services = dict(
            db=await health_service.database_available(),
            redis=await health_service.redis_available(),
        )

    status = "OK"
    for _k, healthy in services.items():
        if healthy is False:
            status = "UNAVAILABLE"
            response.status_code = http_status_code.HTTP_503_SERVICE_UNAVAILABLE

    return HealthCheck(status=status, services=services)
