import logging
import os

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    class Config:
        case_sensitive = True

    LOG_LEVEL: int = int(os.getenv("LOG_LEVEL", logging.INFO))

    # RDS Postgres DB
    DATABASE_USER: str | None = os.getenv("DATABASE_USER")
    DATABASE_PASSWORD: str | None = os.getenv("DATABASE_PASSWORD")
    DATABASE_HOST_READER: str | None = os.getenv("DATABASE_HOST_READER")
    DATABASE_HOST_WRITER: str | None = os.getenv("DATABASE_HOST_WRITER")
    DATABASE_PORT: int = int(os.getenv("DATABASE_PORT", 0))
    DATABASE_NAME: str | None = os.getenv("DATABASE_NAME")
    DATABASE_SCHEMA: str | None = os.getenv("DATABASE_SCHEMA")
    DATABASE_WRITER_URL: str = f"postgresql+asyncpg://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST_WRITER}:{DATABASE_PORT}/{DATABASE_NAME}"
    DATABASE_READER_URL: str = f"postgresql+asyncpg://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST_READER}:{DATABASE_PORT}/{DATABASE_NAME}"

    QUERY_CONNECTION_TIMEOUT: int | None = int(
        os.getenv("QUERY_CONNECTION_TIMEOUT", 30)
    )
    QUERY_COMMAND_TIMEOUT: int | None = int(os.getenv("QUERY_COMMAND_TIMEOUT", 180))

    # LaunchDarkly
    LAUNCH_DARKLY_SDK_KEY: str = os.getenv("LAUNCH_DARKLY_SDK_KEY", "")

    REDIS_HOST: str | None = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int | None = int(os.getenv("REDIS_PORT", 0))

    GRPC_TIMEOUT: int = int(os.getenv("GRPC_TIMEOUT", 10))
    USER_SERVICE_URL: str = os.getenv("USER_SERVICE_URL", "user-service")
    USER_SERVICE_SSL: bool = bool(int(os.getenv("USER_SERVICE_SSL", 1)))
    ORGANIZATION_SERVICE_URL: str = os.getenv("ORGANIZATION_SERVICE_URL", "org-service")
    ORGANIZATION_SERVICE_SSL: bool = bool(int(os.getenv("ORGANIZATION_SERVICE_SSL", 1)))


settings = Settings()
