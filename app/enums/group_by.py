from enum import Enum

from app.models.occupancy import OccupancyAndRevenue

GROUPABLE_COLUMNS = [
    OccupancyAndRevenue.__table__.columns.stay_date.key,
    OccupancyAndRevenue.__table__.columns.room_type.key,
    OccupancyAndRevenue.__table__.columns.reservation_source.key,
    OccupancyAndRevenue.__table__.columns.group_profile_code.key,
]

GroupBy = Enum("GroupBy", {column: column for column in GROUPABLE_COLUMNS})
