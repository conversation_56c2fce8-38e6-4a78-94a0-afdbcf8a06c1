from enum import Enum

from sqlalchemy.orm import DeclarativeBase, class_mapper

from app.models.occupancy import OccupancyAndRevenue


def get_model_columns(model: DeclarativeBase) -> set[str]:
    """
    Extract all visible columns and computed properties from an SQLAlchemy model.
    Excludes columns listed in the model's `__hidden_columns__` attribute.
    """
    hidden_columns = getattr(model, "__hidden_columns__", [])

    return {
        column.key
        for column in list(model.__table__.columns)
        + list(class_mapper(model).iterate_properties)
        if "_summary" not in column.key and column.key not in hidden_columns
    }


def get_column_type(model: DeclarativeBase, column_name: str):
    for column in class_mapper(model).columns:
        if column.key == column_name:
            return column.type


Column = Enum(
    "Columns", {column: column for column in get_model_columns(OccupancyAndRevenue)}
)
