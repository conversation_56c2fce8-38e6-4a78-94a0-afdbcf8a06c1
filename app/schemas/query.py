from datetime import date
from typing import Dict, List, Optional, Union

from fastapi import Header, Query
from pydantic import BaseModel, Field, field_validator, model_validator

from app.common.constants import DEFAULT_PAGINATION_LIMIT
from app.enums.column import Column
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.enums.sort import Sort
from app.models.occupancy import OccupancyAndRevenue


class QueryParams(BaseModel):
    """
    Schema for handling query parameters in API requests.
    """

    organization_id: int = Field(
        Header(..., alias="x-organization-id", description="Organization ID"), gt=0
    )
    property_ids: List[int] = Field(
        Query(alias="propertyIds", description="List of Property IDs"),
        min_length=1,
    )
    columns: List[Column] = Field(
        Query(..., alias="columns", description="Columns to retrieve"),
        min_length=1,
    )
    group_by: Optional[List[GroupBy]] = Field(
        Query(None, alias="groupBy", description="Grouping criteria")
    )
    start_date: date = Field(
        Query(..., alias="startDate", description="Start date (YYYY-MM-DD)")
    )
    end_date: date = Field(
        Query(..., alias="endDate", description="End date (YYYY-MM-DD)")
    )
    granularity: Granularity = Field(
        Query(Granularity.day, alias="granularity"),
        description="Set granularity: year, month, or day",
    )
    room_type_ids: Optional[List[int]] = Field(
        Query(None, alias="roomTypeIds", description="List of Room Type IDs")
    )
    offset: int = Field(
        Query(0, alias="offset"),
        description="Pagination offset (default: 0)",
        ge=0,
        le=100000,
    )
    limit: int = Field(
        Query(DEFAULT_PAGINATION_LIMIT, alias="limit"),
        description="Pagination limit (default: 100, max: 1000)",
        ge=1,
        le=1000,
    )
    sort: Optional[List[Sort]] = Field(
        Query(None, alias="sort", description="Sorting criteria (e.g., adr:asc)")
    )

    @model_validator(mode="after")
    def validate_dates(self):
        """
        Ensure start_date is before end_date.
        """
        if self.start_date > self.end_date:
            raise ValueError("start_date must be before end_date")

        return self

    @model_validator(mode="after")
    def validate_columns_when_groups(self):
        if not self.group_by:
            return self

        columns = {column.value for column in self.columns}
        group_by = {group.value for group in self.group_by}
        allowed_extras = (
            OccupancyAndRevenue.__allowed_non_aggregate_columns_with_group_by__
        )

        for column in columns:
            if column in group_by:
                continue
            if column in allowed_extras:
                continue
            if column not in OccupancyAndRevenue.__aggregate_columns__:
                raise ValueError(
                    f"Invalid request: The following fields cannot be used with group by: {column}"
                )

            if (
                column
                in OccupancyAndRevenue.__meaningless_columns_when_group_by_reservation_source__
                and OccupancyAndRevenue.reservation_source.key in group_by
            ):
                raise ValueError(
                    "Invalid request: The following fields cannot be used with group by: reservation_source"
                )

        return self

    @model_validator(mode="after")
    def validate_sort_fields_in_columns(self):
        if not self.sort:
            return self

        columns = {column.value for column in self.columns}
        sorts = {sort.value.split(":")[0] for sort in self.sort}
        missing = sorts - columns

        if missing:
            raise ValueError(
                f"Invalid sort: These fields must be included in 'columns' to be sortable: {', '.join(sorted(missing))}"
            )

        return self

    @field_validator("group_by")
    @classmethod
    def validate_no_column_overlap(cls, group_by: Optional[List[GroupBy]], values):
        """
        Ensure no field exists in both `columns` and `group_by`.
        """
        columns = values.data.get("columns", [])
        columns = [column.value for column in columns]
        group_by_str = [group.value for group in group_by] if group_by else []
        overlap = set(columns) & set(group_by_str)

        if overlap:
            raise ValueError(
                f"Invalid request: The following fields cannot be in both columns and groupBy: {', '.join(overlap)}"
            )
        return group_by


class QueryResponseSchema(BaseModel):
    """
    Response schema for the query service.
    """

    data: Union[
        Dict[
            str,
            Union[
                Dict[str, Dict[str, Union[int, float, str, None]]],
                Dict[str, Union[int, float, str, None]],
            ],
        ],
        List[Dict[str, Union[int, float, str, None]]],
    ]
    limit: int = 100
    offset: int = 0
    sort: Optional[List[Sort]] = Field(None)
