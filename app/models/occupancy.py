from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, Date, Float, Integer, String, cast, func
from sqlalchemy.orm import column_property, declared_attr

from app.database.base import Base


class OccupancyMixin:
    __hidden_columns__ = []
    __aggregate_columns__ = [
        "occupancy",
        "capacity_count",
        "total_rooms_sold",
        "total_rooms_available",
        "blocked_rooms_count",
        "out_of_service_count",
        "guest_count",
        "adults_count",
        "children_count",
    ]
    __excluded_default_aggregate_columns__ = [
        "occupancy",
    ]
    __allowed_non_aggregate_columns_with_group_by__ = [
        "room_type_id",
        "property_id",
        "organization_id",
    ]
    __translate_columns__ = []
    __sortable_columns__ = ["occupancy"]

    organization_id = Column(
        BigInteger,
        comment="Unique identifier for the organization.",
    )
    property_id = Column(
        BigInteger,
        primary_key=True,
        comment="Unique identifier for the property.",
    )
    room_type_id = Column(
        BigInteger,
        primary_key=True,
        comment="Unique identifier for the room type.",
    )
    room_id = Column(
        String,
        primary_key=True,
        comment="Unique identifier for the room.",
    )
    room_type_short_title = Column(
        String,
        comment="Short title of the room type.",
    )
    room_name = Column(String, comment="Name of the room.")
    group_profile_id = Column(
        BigInteger, comment="Unique identifier for the group profile."
    )
    group_profile = Column(String, comment="Name of the group profile.")
    group_profile_code = Column(String, comment="Code of the group profile.")
    room_type = Column(String, comment="Name of the room type.")
    stay_date = Column(Date, primary_key=True, comment="Date of stay.")
    capacity_count = Column(Integer, comment="Total capacity of the room type.")
    total_rooms_available = Column(Integer, comment="Number of rooms available.")
    total_rooms_sold = Column(Integer, comment="Number of rooms sold.")
    blocked_rooms_count = Column(Integer, comment="Number of rooms blocked.")
    out_of_service_count = Column(
        Integer,
        comment="Number of rooms marked as out of service.",
    )
    adults_count = Column(Integer, comment="Number of adults in the room.")
    children_count = Column(Integer, comment="Number of children in the room.")
    guest_count = Column(Integer, comment="Total number of guests in the room.")

    @declared_attr
    def occupancy(cls):
        """Occupancy: Percentage of rooms sold (rooms sold / capacity)."""
        return column_property(
            cast(func.sum(cls.total_rooms_sold), Float)
            / func.nullif(cast(func.sum(cls.capacity_count), Float), 0)
            * 100
        )


class Occupancy(OccupancyMixin, Base):
    __tablename__ = "occupancy_only"
    __table_args__ = {"schema": "occupancy"}


class OccupancyAndRevenue(OccupancyMixin, Base):
    __tablename__ = "occupancy"
    __table_args__ = {"schema": "occupancy"}
    __revenue_columns__ = [
        "total_room_rate",
        "total_room_revenue",
        "total_other_room_revenue",
        "total_room_revenue_adjustments",
        "total_room_taxes",
        "total_room_fees",
        "total_other_revenue",
        "total_revenue",
        "revpar",
        "adr",
    ]
    __hidden_columns__ = [
        "reservation_source",
    ]
    __aggregate_columns__ = [
        "occupancy",
        "capacity_count",
        "total_rooms_sold",
        "total_rooms_available",
        "blocked_rooms_count",
        "out_of_service_count",
        "total_room_rate",
        "total_room_revenue",
        "total_other_room_revenue",
        "total_room_revenue_adjustments",
        "total_room_taxes",
        "total_room_fees",
        "total_other_revenue",
        "total_revenue",
        "revpar",
        "adr",
        "guest_count",
        "adults_count",
        "children_count",
    ]
    __excluded_default_aggregate_columns__ = [
        "occupancy",
        "revpar",
        "adr",
    ]
    __meaningless_columns_when_group_by_reservation_source__ = [
        "capacity_count",
        "revpar",
    ]
    __sortable_columns__ = ["adr", "revpar", "occupancy"]
    __translate_columns__ = [
        "reservation_source",
    ]

    reservation_source = Column(String, comment="Source of the reservation.")
    total_room_rate = Column(Float, comment="Total room rate per night.")
    total_other_room_revenue = Column(
        Float, comment="Revenue from other room-related charges."
    )
    total_room_revenue_adjustments = Column(
        Float, comment="Total adjustments to room revenue."
    )
    total_room_revenue = Column(Float, comment="Total revenue from room bookings.")
    total_room_taxes = Column(Float, comment="Total taxes applied to the booking.")
    total_room_fees = Column(Float, comment="Total fees applied to the booking.")
    total_other_revenue = Column(Float, comment="Revenue from non-room sources.")
    total_revenue = Column(
        Float, comment="Total revenue from room bookings and non-room sources."
    )

    @declared_attr
    def adr(cls):
        """ADR: Average Daily Rate (room revenue / rooms sold)."""
        return column_property(
            cast(func.sum(cls.total_room_revenue), Float)
            / func.nullif(cast(func.sum(cls.total_rooms_sold), Float), 0)
        )

    @declared_attr
    def revpar(cls):
        """RevPAR: Revenue per available room (room revenue / capacity)."""
        return column_property(
            cast(func.sum(cls.total_room_revenue), Float)
            / func.nullif(cast(func.sum(cls.capacity_count), Float), 0)
        )


class OccupancyRoomsGreen(Base):
    """Green table for room capacity data - optimized for occupancy-only queries."""

    __tablename__ = "occupancy_rooms_green"
    __table_args__ = {"schema": "insights"}

    # Core identifiers
    organization_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the organization."
    )
    property_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the property."
    )
    room_type_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the room type."
    )
    stay_date = Column(Date, primary_key=True, comment="Date of stay.")

    group_profile_id = Column(
        BigInteger, comment="Unique identifier for the group profile."
    )

    # Descriptive fields
    group_profile_name = Column(String, comment="Name of the group profile.")
    room_type = Column(String, comment="Name of the room type.")
    accomodation_kind = Column(
        String, comment="Type of accommodation (Physical/Virtual)."
    )
    room_status = Column(String, comment="Current status of the room.")
    group_profile_code = Column(String, comment="Code of the group profile.")

    # Metrics
    room_adults_count = Column(Integer, comment="Number of adults in the room.")
    room_children_count = Column(Integer, comment="Number of children in the room.")
    room_guest_count = Column(Integer, comment="Total number of guests in the room.")
    room_sold = Column(BigInteger, comment="Number of rooms sold.")
    out_of_service = Column(
        Integer, comment="Number of rooms marked as out of service."
    )
    blocked_room = Column(Integer, comment="Number of rooms blocked.")
    room_available = Column(Integer, comment="Number of rooms available.")
    capacity_count = Column(Integer, comment="Total capacity of the room type.")


class OccupancyAssignmentsGreen(Base):
    """Green table for room assignment/booking data - optimized for occupancy-only queries."""

    __tablename__ = "occupancy_assignments_green"
    __table_args__ = {"schema": "insights"}

    # Core identifiers
    organization_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the organization."
    )
    property_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the property."
    )
    room_type_id = Column(
        BigInteger, primary_key=True, comment="Unique identifier for the room type."
    )
    stay_date = Column(Date, primary_key=True, comment="Date of stay.")

    group_profile_id = Column(
        BigInteger, comment="Unique identifier for the group profile."
    )

    # Descriptive fields
    group_profile_name = Column(String, comment="Name of the group profile.")
    room_type = Column(String, comment="Name of the room type.")
    accomodation_kind = Column(
        String, comment="Type of accommodation (Physical/Virtual)."
    )
    room_status = Column(String, comment="Current status of the room.")
    group_profile_code = Column(String, comment="Code of the group profile.")

    # Metrics
    room_adults_count = Column(Integer, comment="Number of adults in the room.")
    room_children_count = Column(Integer, comment="Number of children in the room.")
    room_guest_count = Column(Integer, comment="Total number of guests in the room.")
    room_sold = Column(BigInteger, comment="Number of rooms sold.")
    out_of_service = Column(
        Integer, comment="Number of rooms marked as out of service."
    )
    blocked_room = Column(Integer, comment="Number of rooms blocked.")
