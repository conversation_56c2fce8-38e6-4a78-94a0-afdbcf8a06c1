import types
import uuid

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

import app.common.context_variables as global_vars
from app.common.constants import X_ORGANIZATION_ID, X_PROPERTY_ID
from app.common.logger import logger
from app.services.token import TokenService


class InitRequestVarsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        initial_g = types.SimpleNamespace()
        initial_g.request_id = str(uuid.uuid4())
        initial_g.origin = request.headers.get("origin", "-")
        initial_g.x_amz_trace_id = request.headers.get("x-amzn-trace-id", "-")
        initial_g.property_id = (
            int(request.headers.get(X_PROPERTY_ID))
            if request.headers.get(X_PROPERTY_ID)
            else None
        )
        initial_g.organization_id = (
            int(request.headers.get(X_ORGANIZATION_ID))
            if request.headers.get(X_ORGANIZATION_ID)
            else None
        )
        initial_g.endpoint = (
            f"{request.method} -> {request.url.path}?{request.query_params}"
        )

        access_token = request.headers.get("authorization")
        if access_token:
            token = TokenService(access_token)
            initial_g.user_email = token.get_email()
            initial_g.user_id = token.get_user_id()

        global_vars.request_global.set(initial_g)

        return await call_next(request)


class LogMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Request):
        response = await call_next(request)
        path = request.url.path

        logger.info(
            f"HTTP request: {request.method} {path}",
            extra={
                "req": {"method": request.method, "url": str(request.url)},
                "path": path,
                "res": {"status_code": response.status_code},
                "property_id": request.headers.get(X_PROPERTY_ID),
                "organization_id": request.headers.get(X_ORGANIZATION_ID),
                "x_amz_trace_id": request.headers.get("x-amzn-trace-id", "-"),
                "origin": request.headers.get("origin", "-"),
            },
        )
        return response
