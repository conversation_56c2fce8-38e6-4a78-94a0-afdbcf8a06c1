[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
authors = [
    {name = "Insights Squad"},
]
license = {text = "MIT"}
requires-python = "<4.0,>=3.10"
dependencies = [
    "fastapi[standard]==0.115.8",
    "pydantic==2.10.6",
    "pydantic-settings==2.0.1",
    "uvicorn==0.23.2",
    "pyjwt==2.8.0",
    "sqlalchemy>=2.0.37",
    "pandas>=2.2.3",
    "alembic>=1.14.1",
    "sqlmodel>=0.0.22",
    "protobuf>=5.28.0,<6.0.0",
    "googleapis-common-protos>=1.66.0",
    "grpcio-reflection>=1.70.0",
    "protos-python==0.377.3",
    "grpcio-health-checking>=1.70.0",
    "asyncpg>=0.30.0",
    "greenlet>=3.1.1",
    "grpc-interceptor>=0.15.4",
    "watchdog[watchmedo]>=6.0.0",
    "avro>=1.12.0",
    "kafka-python>=2.0.3",
    "confluent-kafka[avro,schemaregistry]>=2.8.0",
    "ddtrace>=3.8.0",
    "fastapi-babel>=1.0.0",
    "redis>=6.1.0",
    "pytest-dotenv>=0.5.2",
    "launchdarkly-server-sdk>=9.0.0",
]
name = "occupancy-service"
version = "0.1.0"
description = "Cloudbeds python rest api template"
readme = "README.md"

[project.scripts]
grpc = "app.grpc.server:serve"

[project.urls]
Homepage = "https://github.com/cloudbeds/occupancy-service"
Documentation = "https://github.com/cloudbeds/occupancy-service"

[tool.coverage.run]
branch = true
source= ["app", "tests/unit"]

[tool.coverage.report]
fail_under = 70.00 # FIXME
precision = 2

[tool.isort]
profile = "black"

[tool.ruff]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C408",  # unnecessary `dict` call (rewrite as a literal)
    "C901", # function is too complex
]

[tool.uv.sources]
protos-python = { git = "https://github.com/cloudbeds/protos-python.git" }

[dependency-groups]
dev = [
    "pre-commit==3.3.3",
    "pytest-mock==3.10.0",
    "pytest>8.3",
    "pytest-asyncio==0.21.0",
    "coverage[toml]==7.6.12",
    "mypy==0.982",
    "ruff==0.0.270",
    "black==23.3.0",
    "isort==5.12.0",
    "fakeredis[lua]==2.29.0"
]
