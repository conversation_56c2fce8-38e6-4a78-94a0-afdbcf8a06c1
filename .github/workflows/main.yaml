name: Test, Build, and Publish to Stage Environment

on:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read

env:
  repositories: >-
    ["argocd-occupancy"]
  app_id: 288247
  aws_role_arn: arn:aws:iam::048781935247:role/GH-OIDC-SQUAD-CloudbedsPolicies
  aws_ssm_param_name: /github/app/CBSquadCloudbedsPolicies/private-key

jobs:
  ci-checks:
    name: CI checks
    uses: ./.github/workflows/ci-checks.yaml
    secrets: inherit
    with:
      push: true

  update-deployment-us2:
   name: US2 - Deploy to Stage Environment
   needs: ci-checks
   runs-on: x1-core
   environment: stage
   steps:
     - name: Get GH app token
       id: gh-app-token
       uses: cloudbeds/composite-actions/gh-app-token@v2
       with:
         repositories: >-
           ["argocd-occupancy"]
         app_id: ${{ env.app_id }}
         aws_role_arn: ${{ env.aws_role_arn }}
         aws_ssm_param_name: ${{ env.aws_ssm_param_name }}

     - name: Update application on stage cluster
       uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
       with:
         owner: cloudbeds
         repo: argocd-occupancy
         github_token: ${{ steps.gh-app-token.outputs.github-token }}
         workflow_file_name: update-application.yaml
         client_payload: |
           {
             "cluster": "stage-us2",
             "app_name": "${{ github.event.repository.name }}",
             "image_tag": "${{ github.sha }}"
           }

  update-deployment-us1:
   name: US1 - Deploy to Stage Environment
   needs: update-deployment-us2
   runs-on: x1-core
   environment: stage
   steps:
     - name: Get GH app token
       id: gh-app-token
       uses: cloudbeds/composite-actions/gh-app-token@v2
       with:
         repositories: >-
           ["argocd-occupancy"]
         app_id: ${{ env.app_id }}
         aws_role_arn: ${{ env.aws_role_arn }}
         aws_ssm_param_name: ${{ env.aws_ssm_param_name }}

     - name: Update application on stage cluster
       uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
       with:
         owner: cloudbeds
         repo: argocd-occupancy
         github_token: ${{ steps.gh-app-token.outputs.github-token }}
         workflow_file_name: update-application.yaml
         client_payload: |
           {
             "cluster": "stage-us1",
             "app_name": "${{ github.event.repository.name }}",
             "image_tag": "${{ github.sha }}"
           }
