name: Release tag

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'

permissions:
  id-token: write
  contents: read

env:
  repositories: >-
    ["argocd-occupancy"]
  app_id: 288247
  aws_role_arn: arn:aws:iam::048781935247:role/GH-OIDC-SQUAD-CloudbedsPolicies
  aws_ssm_param_name: /github/app/CBSquadCloudbedsPolicies/private-key

jobs:
  re-tag:
    name: Re-tag image
    runs-on: x1-core
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Re-tag image application image
        uses: cloudbeds/composite-actions/docker/crane-re-tag/aws-ecr@v2
        with:
          image_name: ${{ github.event.repository.name }}
          src_tag: ${{ github.sha }}
          dst_tag: ${{ github.ref_name }}

  update-deployment-us2:
    name: US2 - Deploy to Production Environment
    needs: re-tag
    environment: prod
    runs-on: x1-core
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["argocd-occupancy"]
          app_id: ${{ env.app_id }}
          aws_role_arn: ${{ env.aws_role_arn }}
          aws_ssm_param_name: ${{ env.aws_ssm_param_name }}

      - name: Update deployment manifest in prod-us2
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-occupancy
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          ref: main
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "prod-us2",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.ref_name }}"
            }

  update-deployment-us1:
    name: US1 - Deploy to Production Environment
    needs: update-deployment-us2
    environment: prod
    runs-on: x1-core
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["argocd-occupancy"]
          app_id: ${{ env.app_id }}
          aws_role_arn: ${{ env.aws_role_arn }}
          aws_ssm_param_name: ${{ env.aws_ssm_param_name }}

      - name: Update deployment manifest in prod-us1
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-occupancy
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          ref: main
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "prod-us1",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.ref_name }}"
            }
