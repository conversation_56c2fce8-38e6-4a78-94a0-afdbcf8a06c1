# Occupancy Service

REST API and gRPC service for managing occupancy data at Cloudbeds.

## Documentation

- [REST API Documentation](https://api.cloudbeds.com/occupancy)
- [Proto Definitions](https://github.com/cloudbeds/protos)

## Steps to Install the Project

1. Be sure you have [uv](https://github.com/astral-sh/uv) installed
2. Run `make install` to set up environment and build Docker containers
3. Alternatively, run `uv sync` to install dependencies without Docker

## Building and Running

### With Docker
```bash
# Start the services
make start

# Stop the services
make stop

# Recreate services (full rebuild)
make recreate
```

### Without Docker
```bash
# Run REST API in development mode
make run-api
# or
uv run fastapi dev

# Run gRPC server with auto-reload
make run-grpc
```

## Development

### Testing
```bash
# Run all tests with coverage
make unit-test

# Run a specific test
uv run -m pytest -vv tests/unit/path/to/test_file.py::TestClass::test_method
```

### Linting
```bash
make lint
```

### Database Migrations
```bash
# Create migration
make migrations-revision-autogenerate message="Migration description"

# Apply migrations
make migrations-upgrade
```

### Remote Testing with gRPC

To test against the stage environment:

1. Authenticate with AWS and switch to the `stage-us2` context
2. Set up port forwarding to the gRPC service:
   ```bash
   kubectl port-forward -n occupancy deployment/occupancy-grpc 50051:50051
   ```
3. Connect to the forwarded port with Postman:
   - Use `localhost:50051` as the server
   - Click "Disable TLS" in the connection settings
   - Use proto definitions from the [Cloudbeds protos repository](https://github.com/cloudbeds/protos)

## VS Code Setup

1. Select the correct Python interpreter: `Ctrl/Cmd + Shift + P -> Select Python Interpreter -> Enter Interpreter path`
2. Configure test explorer: `Ctrl/Cmd + Shift + P -> Configure Python Tests -> Select pytest -> Select tests`
