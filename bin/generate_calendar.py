import os
from datetime import date, datetime, timedelta

from confluent_kafka import <PERSON>f<PERSON><PERSON><PERSON><PERSON>, KafkaException, Producer
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroSerializer
from confluent_kafka.serialization import <PERSON><PERSON><PERSON>, SerializationContext


def daterange(start: date, end: date):
    days = int((end - start).days)
    for n in range(days):
        yield start + timedelta(n)


class CalendarKey:
    def __init__(self, calendar_date):
        self.date = calendar_date


def key_to_dict(k, ctx):
    return dict(date=k.date)


class CalendarValue:
    def __init__(self, calendar_date):
        self.date = calendar_date


def value_to_dict(v, ctx):
    return dict(
        date=v.date,
        year=v.date.year,
        month=v.date.month,
        day=v.date.day,
        day_of_week=v.date.weekday(),
        unix_timestamp=int(v.date.strftime("%s")),
    )


def error_cb(err):
    """The error callback is used for generic client errors. These
    errors are generally to be considered informational as the client will
    automatically try to recover from all errors, and no extra action
    is typically required by the application.
    For this example however, we terminate the application if the client
    is unable to connect to any broker (_ALL_BROKERS_DOWN) and on
    authentication errors (_AUTHENTICATION)."""

    print("Client error: {}".format(err))
    if (
        err.code() == KafkaError._ALL_BROKERS_DOWN
        or err.code() == KafkaError._AUTHENTICATION
    ):
        # Any exception raised from this callback will be re-raised from the
        # triggering flush() or poll() call.
        raise KafkaException(err)


def delivery_report(err, msg):
    """
    Reports the failure or success of a message delivery.

    Args:
        err (KafkaError): The error that occurred on None on success.

        msg (Message): The message that was produced or failed.

    Note:
        In the delivery report callback the Message.key() and Message.value()
        will be the binary format as encoded by any configured Serializers and
        not the same object that was passed to produce().
        If you wish to pass the original object(s) for key and value to delivery
        report callback we recommend a bound callback or lambda where you pass
        the objects along.
    """

    if err is not None:
        print("Delivery failed for record {}: {}".format(msg.key(), err))
        return
    print(
        "record {} successfully produced to {} [{}] at offset {}".format(
            msg.key(), msg.topic(), msg.partition(), msg.offset()
        )
    )


def main():
    topic = os.getenv("CALENDAR_TOPIC")
    start_date = datetime.strptime(os.getenv("CALENDAR_START_DATE"), "%Y-%m-%d")
    end_date = datetime.strptime(os.getenv("CALENDAR_END_DATE"), "%Y-%m-%d")

    print(f"Producing records to topic {topic} from {start_date} to {end_date}")

    schema_registry_client = SchemaRegistryClient(
        {
            "url": os.getenv("SCHEMA_REGISTRY_URL"),
            "basic.auth.user.info": f"{os.getenv('SCHEMA_REGISTRY_USERNAME')}:{os.getenv('SCHEMA_REGISTRY_PASSWORD')}",
        }
    )

    path = os.path.realpath(os.path.dirname(__file__))
    with open(f"{path}/calendar-key.avsc") as f:
        key_schema = f.read()
    with open(f"{path}/calendar-value.avsc") as f:
        value_schema = f.read()

    key_serializer = AvroSerializer(schema_registry_client, key_schema, key_to_dict)

    value_serializer = AvroSerializer(
        schema_registry_client, value_schema, value_to_dict
    )

    producer = Producer(
        {
            "bootstrap.servers": os.getenv("KAFKA_BROKER"),
            "sasl.mechanism": "PLAIN",
            "security.protocol": "SASL_SSL",
            "sasl.username": os.getenv("KAFKA_USERNAME"),
            "sasl.password": os.getenv("KAFKA_PASSWORD"),
            "error_cb": error_cb,
        }
    )

    for single_date in daterange(start_date, end_date):
        producer.poll(0.0)
        key = CalendarKey(single_date)
        value = CalendarValue(single_date)

        producer.produce(
            topic=topic,
            key=key_serializer(key, SerializationContext(topic, MessageField.KEY)),
            value=value_serializer(
                value, SerializationContext(topic, MessageField.VALUE)
            ),
            on_delivery=delivery_report,
        )

    print("Flushing records...")
    producer.flush()


if __name__ == "__main__":
    main()
