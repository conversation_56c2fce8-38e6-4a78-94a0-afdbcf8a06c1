import datetime

import pytest
import sqlalchemy
from cloudbeds.occupancy.v1 import occupancy_pb2
from sqlalchemy.sql.sqltypes import BigI<PERSON>ger, Date, Float, Integer, String

from app.enums.column import Column, get_column_type
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.grpc.service.occupancy import OccupancyService
from app.models.occupancy import OccupancyAndRevenue
from app.schemas.query import QueryParams
from conftest import AsyncMockSession


def create_value_for_column(column):
    t = get_column_type(OccupancyAndRevenue, column.value)
    if isinstance(t, Float) or isinstance(t, sqlalchemy.FLOAT):
        return 1.0
    elif isinstance(t, Integer) or isinstance(t, BigInteger):
        return 1
    elif isinstance(t, String):
        return "test"
    elif isinstance(t, Date):
        return datetime.date(2023, 1, 1)
    else:
        raise ValueError(f"Unknown column type t={t}, type={type(t)}")


@pytest.mark.usefixtures("clean_cache")
class TestOccupancyService:
    @pytest.mark.asyncio
    async def test_to_query(self, occupancy_service, query_request):
        query_params = OccupancyService()._OccupancyService__to_query(query_request)
        assert isinstance(query_params, QueryParams)
        assert query_params.organization_id == query_request.organization_id
        assert query_params.property_ids == query_request.property_ids
        assert query_params.start_date == datetime.date(2023, 1, 1)
        assert query_params.end_date == datetime.date(2023, 1, 31)
        assert query_params.granularity == Granularity.day
        assert query_params.room_type_ids == [1]
        assert query_params.offset == 0
        assert query_params.limit == 10

    @pytest.mark.asyncio
    async def test_to_query_reservation_sources(
        self, occupancy_service, query_reservation_sources_request
    ):
        query_params = (
            OccupancyService()._OccupancyService__to_query_reservation_sources(
                query_reservation_sources_request
            )
        )

        assert isinstance(query_params, QueryParams)
        assert (
            query_params.organization_id
            == query_reservation_sources_request.organization_id
        )
        assert (
            query_params.property_ids == query_reservation_sources_request.property_ids
        )
        assert query_params.start_date == datetime.date(2023, 1, 1)
        assert query_params.end_date == datetime.date(2023, 1, 31)
        assert query_params.granularity == Granularity.day
        assert query_params.group_by == [GroupBy.reservation_source]
        assert Column.adr in query_params.columns
        assert query_params.offset == 0
        assert query_params.limit == 10

    @pytest.mark.parametrize("column", Column)
    def test_fill_data(self, column):
        try:
            data = occupancy_pb2.Data()
            value = create_value_for_column(column)
            OccupancyService()._OccupancyService__fill_data(column.value, value, data)
        except ValueError:
            pytest.fail(f"__fil_data raised ValueError for {column}")

    @pytest.mark.asyncio
    async def test_convert_to_proto_response_list(self, occupancy_service):
        query_result = ([{"revpar": 12}], 10, 0, [])
        response = OccupancyService().convert_to_proto_response(query_result)
        assert isinstance(response, occupancy_pb2.QueryResponse)
        assert response.limit == 10
        assert response.offset == 0
        assert response.HasField("list") is True
        assert response.list.data[0].revpar == 12

    @pytest.mark.asyncio
    async def test_convert_to_proto_response_summary(self, occupancy_service):
        query_result = ({"Queen Deluxe": {"revpar": 1000}}, 10, 0, [])
        response = OccupancyService().convert_to_proto_response(query_result)
        assert isinstance(response, occupancy_pb2.QueryResponse)
        assert response.limit == 10
        assert response.offset == 0
        assert response.HasField("summary") is True
        assert response.summary.groups["Queen Deluxe"].data.revpar == 1000

    @pytest.mark.asyncio
    async def test_execute_query_reservation_sources(self, monkeypatch):
        service = OccupancyService()

        mock_data = {
            "Website": {"adr": 123.45, "revpar": 67.89},
            "Phone": {"adr": 200.0, "revpar": 100.0},
        }

        async def mock_get(*args, **kwargs):
            return mock_data, 10, 0, []

        monkeypatch.setattr(
            "app.grpc.service.occupancy.get_session", lambda: AsyncMockSession()
        )
        monkeypatch.setattr("app.services.query.QueryService.get", mock_get)

        params = QueryParams(
            organization_id=1,
            property_ids=[1],
            start_date=datetime.date(2023, 1, 1),
            end_date=datetime.date(2023, 1, 31),
            granularity=Granularity.day,
            columns=[Column.adr, Column.guest_count],
            group_by=[GroupBy.reservation_source],
            room_type_ids=[],
            offset=0,
            limit=10,
            sort=[],
        )

        response = await service._OccupancyService__execute_query_reservation_sources(
            params
        )

        assert isinstance(response, occupancy_pb2.QueryReservationSourcesResponse)
        assert response.limit == 10
        assert response.offset == 0
        assert len(response.reservation_sources) == 2
        assert response.reservation_sources[0].reservation_source in [
            "Website",
            "Phone",
        ]

    @pytest.mark.asyncio
    async def test_query_reservation_sources(
        self, query_reservation_sources_request, monkeypatch
    ):
        service = OccupancyService()

        async def mock_get(*args, **kwargs):
            return (
                {
                    "sources_walk_in": {"adr": 99.0, "revpar": 50.0},
                },
                10,
                0,
                [],
            )

        monkeypatch.setattr(
            "app.grpc.service.occupancy.get_session", lambda: AsyncMockSession()
        )
        monkeypatch.setattr("app.services.query.QueryService.get", mock_get)

        response = await service.query_reservation_sources(
            query_reservation_sources_request
        )

        assert isinstance(response, occupancy_pb2.QueryReservationSourcesResponse)
        assert response.limit == 10
        assert response.offset == 0
        assert response.reservation_sources[0].reservation_source == "sources_walk_in"
        assert response.reservation_sources[0].data.adr == 99.0
