import datetime

import pytest
from cloudbeds.occupancy.v1 import occupancy_pb2
from google.type import date_pb2

from app.enums.column import Column
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.grpc.protobuf.converters import (
    column_enum_to_proto,
    column_proto_to_enum,
    granularity_enum_to_proto,
    granularity_proto_to_enum,
    group_by_enum_to_proto,
    group_by_proto_to_enum,
    to_proto_date,
    to_python_date,
)


def test_to_python_date():
    proto_date = date_pb2.Date(year=2023, month=10, day=5)
    expected_date = datetime.date(2023, 10, 5)
    assert to_python_date(proto_date) == expected_date


def test_to_proto_date():
    python_date = datetime.date(2023, 10, 5)
    expected_proto_date = date_pb2.Date(year=2023, month=10, day=5)
    assert to_proto_date(python_date) == expected_proto_date


@pytest.mark.parametrize("proto_enum", occupancy_pb2.Granularity.values())
def test_granularity_proto_to_enum(proto_enum):
    try:
        granularity_proto_to_enum(proto_enum)
    except ValueError:
        name = occupancy_pb2.Granularity.Name(proto_enum)
        pytest.fail(f"granularity_proto_to_enum raised ValueError for {name}")


@pytest.mark.parametrize("enum", Granularity)
def test_granularity_enum_to_proto(enum):
    try:
        granularity_enum_to_proto(enum)
    except ValueError:
        pytest.fail(f"granularity_enum_to_proto raised ValueError for {enum}")


@pytest.mark.parametrize("proto_enum", occupancy_pb2.GroupBy.values())
def test_group_by_proto_to_enum(proto_enum):
    try:
        group_by_proto_to_enum(proto_enum)
    except ValueError:
        name = occupancy_pb2.GroupBy.Name(proto_enum)
        pytest.fail(f"group_by_proto_to_enum raised ValueError for {name}")


@pytest.mark.parametrize("enum", GroupBy)
def test_group_by_enum_to_proto(enum):
    try:
        group_by_enum_to_proto(enum.value)
    except ValueError:
        pytest.fail(f"group_by_enum_to_proto raised ValueError for {enum}")


@pytest.mark.parametrize("proto_enum", occupancy_pb2.Column.values())
def test_column_proto_to_enum(proto_enum):
    try:
        column_proto_to_enum(proto_enum)
    except ValueError:
        name = occupancy_pb2.Column.Name(proto_enum)
        pytest.fail(f"column_proto_to_enum raised ValueError for {name}")


@pytest.mark.parametrize("enum", Column)
def test_column_enum_to_proto(enum):
    try:
        column_enum_to_proto(enum.value)
    except ValueError:
        pytest.fail(f"column_enum_to_proto raised ValueError for {enum}")
