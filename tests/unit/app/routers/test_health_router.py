from unittest.mock import AsyncMock

import pytest


@pytest.mark.usefixtures("clean_cache")
class TestHealthRouter:
    @pytest.mark.asyncio
    async def test_healthy(self, test_client, mocker):
        mocker.patch(
            "app.routers.health.HealthService.database_available",
            AsyncMock(return_value=True),
        )
        mocker.patch(
            "app.routers.health.HealthService.redis_available",
            AsyncMock(return_value=True),
        )
        response = test_client.get("/occupancy/v1/health")

        assert response.status_code == 200
        assert response.json() == {
            "status": "OK",
            "services": {"db": True, "redis": True},
        }

    @pytest.mark.asyncio
    async def test_unhealth(self, test_client, mocker):
        mocker.patch(
            "app.routers.health.HealthService.database_available",
            AsyncMock(return_value=False),
        )
        mocker.patch(
            "app.routers.health.HealthService.redis_available",
            AsyncMock(return_value=False),
        )
        response = test_client.get("/occupancy/v1/health")

        assert response.status_code == 503
        assert response.json() == {
            "status": "UNAVAILABLE",
            "services": {"db": False, "redis": False},
        }
