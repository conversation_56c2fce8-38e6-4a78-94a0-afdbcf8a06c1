from unittest.mock import AsyncMock

import pytest

from tests.unit.fixtures.access_token import SUPER_ADMIN_TOKEN


@pytest.mark.usefixtures("clean_cache")
class TestQueryRouter:
    @pytest.mark.asyncio
    async def test_query_grouped(self, test_client, fake_row, mocker):
        mocker.patch(
            "app.routers.query.QueryService._QueryService__execute_query",
            AsyncMock(return_value=[fake_row]),
        )
        response = test_client.get(
            "/occupancy/v1/query",
            headers={
                "Authorization": f"Bearer {SUPER_ADMIN_TOKEN}",
                "x-organization-id": "177159",
            },
            params={
                "columns": ["occupancy", "adr", "revpar"],
                "groupBy": ["stay_date"],
                "startDate": "2025-01-01",
                "endDate": "2025-01-02",
                "propertyIds": "22425",
            },
        )

        expected = {
            "data": {
                "2024-05-01": {
                    "organization_id": "177159",
                    "property_id": "22425",
                    "occupancy": "0.0",
                    "room_type": "Single room",
                },
            },
            "limit": 100,
            "offset": 0,
            "sort": [],
        }

        assert response.json() == expected
        assert response.status_code == 200
