from app.enums.column import get_model_columns
from app.models.occupancy import OccupancyAndRevenue


class TestColumn:
    def test_get_model_columns(self):
        columns = get_model_columns(OccupancyAndRevenue)
        assert columns == {
            "total_room_fees",
            "organization_id",
            "total_other_room_revenue",
            "total_rooms_sold",
            "out_of_service_count",
            "capacity_count",
            "group_profile_code",
            "group_profile_id",
            "group_profile",
            "adults_count",
            "room_type",
            "room_id",
            "room_type_short_title",
            "room_name",
            "adr",
            "revpar",
            "room_type_id",
            "total_room_rate",
            "children_count",
            "total_other_revenue",
            "occupancy",
            "total_revenue",
            "blocked_rooms_count",
            "property_id",
            "total_room_revenue",
            "total_room_taxes",
            "stay_date",
            "total_rooms_available",
            "guest_count",
            "total_room_revenue_adjustments",
        }
