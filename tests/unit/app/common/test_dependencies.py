from http import HTTPStatus
from unittest.mock import MagicMock

import pytest
from fastapi import <PERSON>TT<PERSON><PERSON>xception
from fastapi.security import HTTPAuthorizationCredentials

from app.common.dependencies import authorization
from app.services.token import TokenService
from tests.unit.fixtures.access_token import SUPER_ADMIN_TOKEN, USER_TOKEN


class TestDependencies:
    def test_authorization_missing_organization_id(self):
        with pytest.raises(HTTPException) as exc:
            authorization(organizationId=None, propertyIds=[1], token=MagicMock())

        assert exc.value.status_code == HTTPStatus.BAD_REQUEST
        assert exc.value.detail == "x-organization-id header is missing"

    def test_authorization_missing_property_ids(self):
        with pytest.raises(HTTPException) as exc:
            authorization(organizationId=100, propertyIds=[], token=MagicMock())

        assert exc.value.status_code == HTTPStatus.BAD_REQUEST
        assert exc.value.detail == "propertyIds query param is missing or empty"

    def test_authorization_missing_token(self):
        with pytest.raises(HTTPException) as exc:
            authorization(organizationId=100, propertyIds=[1], token=None)

        assert exc.value.status_code == HTTPStatus.UNAUTHORIZED
        assert exc.value.detail == "Access token is missing"

    def test_authorization_super_admin(self, mocker):
        mocker.patch.object(TokenService, "is_super_admin", return_value=True)

        token_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer", credentials=SUPER_ADMIN_TOKEN
        )
        result = authorization(
            organizationId=100, propertyIds=[1, 2], token=token_credentials
        )

        assert result == {"organization_id": 100, "token": token_credentials}

    def test_authorization_valid_user(self, mocker, mock_user_service):
        mocker.patch.object(TokenService, "is_super_admin", return_value=False)
        mocker.patch.object(TokenService, "get_user_id", return_value=123)

        mock_user_service(property_ids=[1, 2, 3], organization_ids=[100, 200])

        token_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer", credentials=USER_TOKEN
        )
        result = authorization(
            organizationId=100, propertyIds=[1, 2], token=token_credentials
        )

        assert result == {"organization_id": 100, "token": token_credentials}

    def test_authorization_unauthorized_organization(self, mocker, mock_user_service):
        mocker.patch.object(TokenService, "is_super_admin", return_value=False)
        mocker.patch.object(TokenService, "get_user_id", return_value=123)

        mock_user_service(property_ids=[1, 2, 3], organization_ids=[200, 300])

        with pytest.raises(HTTPException) as exc:
            token_credentials = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=USER_TOKEN
            )
            authorization(
                organizationId=100, propertyIds=[1, 2], token=token_credentials
            )

        assert exc.value.status_code == HTTPStatus.UNAUTHORIZED
        assert (
            exc.value.detail
            == "Organization ID 100 provided is not authorized via access token"
        )

    def test_authorization_unauthorized_property(self, mocker, mock_user_service):
        mocker.patch.object(TokenService, "is_super_admin", return_value=False)
        mocker.patch.object(TokenService, "get_user_id", return_value=123)

        mock_user_service(property_ids=[1, 2], organization_ids=[100, 200])

        with pytest.raises(HTTPException) as exc:
            token_credentials = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=USER_TOKEN
            )
            authorization(
                organizationId=100, propertyIds=[1, 2, 3], token=token_credentials
            )

        assert exc.value.status_code == HTTPStatus.UNAUTHORIZED
        assert (
            exc.value.detail
            == "Property ID 3 provided is not authorized via access token"
        )
