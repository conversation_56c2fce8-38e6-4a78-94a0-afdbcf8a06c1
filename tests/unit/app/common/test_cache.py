import json
from unittest.mock import AsyncMock, patch

import pytest

from app.common.cache import Cache


class TestCache:
    @pytest.mark.asyncio
    async def test_cache_with_results(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(
                return_value=json.dumps({"data": "value"})
            )

            cache = Cache()
            result = await cache.get("key")
            assert result["data"] == "value"

    @pytest.mark.asyncio
    async def test_cache_without_results(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(return_value=None)

            cache = Cache()
            result = await cache.get("key")
            assert result is None

    @pytest.mark.asyncio
    async def test_memoize_decorator_no_result(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(return_value=None)
            mock_redis_instance.set = AsyncMock(return_value=None)

            cache = Cache()

            @cache.memoize()
            async def test_function():
                return {"data": "value"}

            result = await test_function()
            assert result["data"] == "value"
            mock_redis_instance.set.assert_called_once()
            mock_redis_instance.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_memoize_decorator_with_result(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(
                return_value=json.dumps({"data": "value"})
            )

            cache = Cache()

            @cache.memoize()
            async def test_function():
                return {"data": "value"}

            result = await test_function()
            assert result["data"] == "value"
            mock_redis_instance.set.assert_not_called()
            mock_redis_instance.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_memoize_key_generation_with_instance_and_kwargs(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(return_value=None)
            mock_redis_instance.set = AsyncMock(return_value=None)

            cache = Cache()

            class Dummy:
                organization_id = 1
                property_ids = [2]

                @cache.memoize()
                async def run(self, a, b=None):
                    return a + 1

            obj = Dummy()
            result = await obj.run(10, b="test")

            assert result == 11

            called_key = mock_redis_instance.set.call_args[0][0]
            assert "organization_id=1" in called_key
            assert "property_ids=2" in called_key
            assert "arg0=10" in called_key
            assert "b=test" in called_key

    @pytest.mark.asyncio
    async def test_memoize_drops_empty_values(self):
        with patch("redis.asyncio.StrictRedis") as MockStrictRedis:
            mock_redis_instance = MockStrictRedis.return_value
            mock_redis_instance.get = AsyncMock(return_value=None)
            mock_redis_instance.set = AsyncMock(return_value=None)

            cache = Cache()

            class Dummy:
                organization_id = 1
                property_ids = [1]

                @cache.memoize()
                async def compute(self, x=None, y=None):
                    return "done"

            obj = Dummy()
            await obj.compute(x=None, y="")

            key_used = mock_redis_instance.set.call_args[0][0]
            assert "x=" not in key_used
            assert "y=" not in key_used
            assert "organization_id=1" in key_used
