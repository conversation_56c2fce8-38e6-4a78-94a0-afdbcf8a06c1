from cloudbeds.organization.v1 import common_pb2, property_pb2
from cloudbeds.user.v1 import user_pb2

from app.services.user_service import UserService
from tests.unit.data.parameters import (
    ORGANI<PERSON>ATION_ID,
    ORGANIZATION_ID_2,
    PROPERTY_ID,
    PROPERTY_ID_2,
    PROPERTY_ID_3,
)


class TestUserService:
    def test_get_user_properties_single_property(
        self, user_service_client, property_service_client
    ):
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()
        property = user_pb2.UserProperty()
        property.id = PROPERTY_ID
        user_assignment.properties.append(property)
        user_assignment_response.assignment.CopyFrom(user_assignment)
        user_service_client(user_assignment_response)

        list_properties_response = property_pb2.ListPropertiesResponse()
        organization = common_pb2.Organization()
        organization.id = ORGANIZATION_ID
        property = common_pb2.Property()
        property.organization.CopyFrom(organization)
        list_properties_response.properties.append(property)
        property_service_client(list_properties_response)

        properties, organizations = UserService.get_user_assignment(
            1,
        )
        assert properties == [PROPERTY_ID]
        assert organizations == [ORGANIZATION_ID]

    def test_get_user_assignment_empty(self, user_service_client):
        """Test when user has no properties assigned"""
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()
        user_assignment_response.assignment.CopyFrom(user_assignment)
        user_service_client(user_assignment_response)

        properties, organizations = UserService.get_user_assignment(1)

        assert properties == []
        assert organizations == []

    def test_property_service_returns_none(
        self, user_service_client, property_service_client
    ):
        """Test when property service returns None for property IDs"""
        # Setup user assignment with a property
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()
        property = user_pb2.UserProperty()
        property.id = PROPERTY_ID
        user_assignment.properties.append(property)
        user_assignment_response.assignment.CopyFrom(user_assignment)
        user_service_client(user_assignment_response)

        # Setup property service to return None
        property_service_client(None)

        # Call the method and verify results
        properties, organizations = UserService.get_user_assignment(1)

        # Should return property IDs but empty organization IDs
        assert properties == [PROPERTY_ID]
        assert organizations == []

    def test_get_user_assignment_multiple_properties(
        self, user_service_client, property_service_client
    ):
        """Test when user has multiple properties assigned"""
        # Setup user assignment with multiple properties
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()

        # Add first property
        property1 = user_pb2.UserProperty()
        property1.id = PROPERTY_ID
        user_assignment.properties.append(property1)

        # Add second property
        property2 = user_pb2.UserProperty()
        property2.id = PROPERTY_ID_2
        user_assignment.properties.append(property2)

        user_assignment_response.assignment.CopyFrom(user_assignment)
        user_service_client(user_assignment_response)

        # Setup property service response
        list_properties_response = property_pb2.ListPropertiesResponse()

        # First property
        organization1 = common_pb2.Organization()
        organization1.id = ORGANIZATION_ID
        property_obj1 = common_pb2.Property()
        property_obj1.organization.CopyFrom(organization1)
        list_properties_response.properties.append(property_obj1)

        # Second property - same organization for simplicity
        organization2 = common_pb2.Organization()
        organization2.id = ORGANIZATION_ID
        property_obj2 = common_pb2.Property()
        property_obj2.organization.CopyFrom(organization2)
        list_properties_response.properties.append(property_obj2)

        property_service_client(list_properties_response)

        # Call the method and verify results
        properties, organizations = UserService.get_user_assignment(1)

        # Should return both property IDs and the organization ID
        assert properties == [PROPERTY_ID, PROPERTY_ID_2]
        assert organizations == [ORGANIZATION_ID, ORGANIZATION_ID]

    def test_get_user_assignment_with_organizations(self, user_service_client):
        """Test when user has organizations assigned directly"""
        # Setup user assignment with organizations
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()

        # Create organization with properties
        organization = user_pb2.UserOrganization()
        organization.id = ORGANIZATION_ID

        # Add properties to the organization
        property1 = user_pb2.UserProperty()
        property1.id = PROPERTY_ID
        organization.properties.append(property1)

        property2 = user_pb2.UserProperty()
        property2.id = PROPERTY_ID_2
        organization.properties.append(property2)

        # Add organization to assignment
        user_assignment.organizations.append(organization)
        user_assignment_response.assignment.CopyFrom(user_assignment)

        # Mock the user service client
        user_service_client(user_assignment_response)

        # Call the method and verify results
        properties, organizations = UserService.get_user_assignment(1)

        # Should return both property IDs and the organization ID
        assert properties == [PROPERTY_ID, PROPERTY_ID_2]
        assert organizations == [ORGANIZATION_ID]

    def test_get_user_assignment_with_multiple_organizations(self, user_service_client):
        """Test when user has multiple organizations assigned directly"""
        # Setup user assignment with multiple organizations
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()

        # Create first organization with properties
        organization1 = user_pb2.UserOrganization()
        organization1.id = ORGANIZATION_ID

        # Add property to first organization
        property1 = user_pb2.UserProperty()
        property1.id = PROPERTY_ID
        organization1.properties.append(property1)

        # Create second organization with different properties
        organization2 = user_pb2.UserOrganization()
        organization2.id = ORGANIZATION_ID_2

        # Add properties to second organization
        property2 = user_pb2.UserProperty()
        property2.id = PROPERTY_ID_2
        organization2.properties.append(property2)

        property3 = user_pb2.UserProperty()
        property3.id = PROPERTY_ID_3
        organization2.properties.append(property3)

        # Add organizations to assignment
        user_assignment.organizations.append(organization1)
        user_assignment.organizations.append(organization2)
        user_assignment_response.assignment.CopyFrom(user_assignment)

        # Mock the user service client
        user_service_client(user_assignment_response)

        # Call the method and verify results
        properties, organizations = UserService.get_user_assignment(1)

        # Should return all property IDs and both organization IDs
        assert sorted(properties) == sorted([PROPERTY_ID, PROPERTY_ID_2, PROPERTY_ID_3])
        assert sorted(organizations) == sorted([ORGANIZATION_ID, ORGANIZATION_ID_2])

    def test_get_user_assignment_with_empty_organizations(
        self, user_service_client, property_service_client
    ):
        """Test when user has empty organizations list but has direct properties"""
        # Setup user assignment with empty organizations list
        user_assignment_response = user_pb2.GetUserAssignmentResponse()
        user_assignment = user_pb2.UserAssignment()

        # Add properties directly (not via organizations)
        property1 = user_pb2.UserProperty()
        property1.id = PROPERTY_ID
        user_assignment.properties.append(property1)

        # Create empty organizations list
        # This simulates the case where organizations field exists but is empty
        # The service should fall back to using property_service
        user_assignment_response.assignment.CopyFrom(user_assignment)
        user_service_client(user_assignment_response)

        # Setup property service response
        list_properties_response = property_pb2.ListPropertiesResponse()
        organization = common_pb2.Organization()
        organization.id = ORGANIZATION_ID
        property_obj = common_pb2.Property()
        property_obj.organization.CopyFrom(organization)
        list_properties_response.properties.append(property_obj)
        property_service_client(list_properties_response)

        # Call the method and verify results
        properties, organizations = UserService.get_user_assignment(1)

        # Should use the property service to get organization IDs
        assert properties == [PROPERTY_ID]
        assert organizations == [ORGANIZATION_ID]
