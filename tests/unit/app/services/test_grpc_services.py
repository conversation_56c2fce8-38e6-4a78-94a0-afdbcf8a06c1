import pytest
from fastapi import HTT<PERSON>Exception

from app.services.grpc import PropertyServiceClient, UserServiceClient


class TestGRPCServices:
    @pytest.mark.usefixtures("mock_broken_grpc_not_found")
    def test_grpc_service_client_error_not_found(self):
        # Property Service Client
        with pytest.raises(HTTPException):
            assert PropertyServiceClient().get_properties_by_ids(1)

        # User Service Client
        assert UserServiceClient().get_assignment_by_user_id(1) is None

    @pytest.mark.usefixtures("mock_broken_grpc_unavailable")
    def test_grpc_service_client_error_not_unavailable(self):
        # Property Service Client
        with pytest.raises(HTTPException):
            assert PropertyServiceClient().get_properties_by_ids([1])

        # User Service Client
        with pytest.raises(HTTPException):
            assert UserServiceClient().get_assignment_by_user_id(1)

    @pytest.mark.usefixtures("mock_broken_grpc_generic")
    def test_grpc_service_client_error_generic_exception(self):
        # Property Service Client
        with pytest.raises(HTTPException):
            assert PropertyServiceClient().get_properties_by_ids(1)

        # User Service Client

        with pytest.raises(HTTPException):
            assert UserServiceClient().get_assignment_by_user_id(1)
