"""
Tests for CTERevenue class.
"""

from datetime import date

import pytest
from sqlalchemy.sql.selectable import CTE

from app.services.query_builders.cte_revenue import CTERevenue


class TestCTERevenue:
    """Test cases for CTERevenue class."""

    @pytest.fixture
    def cte_revenue(self):
        """Create a CTERevenue instance for testing."""
        return CTERevenue(property_ids=[208095, 208096], organization_id=208095)

    def test_init(self, cte_revenue):
        """Test CTERevenue initialization."""
        assert cte_revenue.property_ids == [208095, 208096]
        assert cte_revenue.organization_id == 208095

    def test_build_basic_revenue_cte(self, cte_revenue):
        """Test building a basic revenue CTE."""
        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            grouping_columns=None,
        )

        assert isinstance(cte, CTE)
        assert cte.name == "revenue_aggregated"

        # Check that the CTE contains expected columns
        cte_str = str(cte)
        assert "total_sold" in cte_str
        assert "total_guests" in cte_str
        assert "total_room_revenue" in cte_str

    def test_build_revenue_cte_with_room_type_filter(self, cte_revenue):
        """Test building revenue CTE with room type filter."""
        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=[1, 2, 3],
            grouping_columns=None,
        )

        assert isinstance(cte, CTE)
        cte_str = str(cte)
        # Should include room_type_id filter
        assert "room_type_id" in cte_str

    def test_build_revenue_cte_with_custom_grouping(self, cte_revenue):
        """Test building revenue CTE with custom grouping columns."""
        from app.models.occupancy import OccupancyAssignmentsGreen

        custom_grouping = {
            "organization_id": OccupancyAssignmentsGreen.organization_id,
            "property_id": OccupancyAssignmentsGreen.property_id,
            "room_type_id": OccupancyAssignmentsGreen.room_type_id,
            "stay_date": OccupancyAssignmentsGreen.stay_date,
            "room_type": OccupancyAssignmentsGreen.room_type,
        }

        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            grouping_columns=custom_grouping,
        )

        assert isinstance(cte, CTE)
        cte_str = str(cte)
        # Should include room_type in grouping
        assert "room_type" in cte_str

    def test_build_revenue_cte_joins(self, cte_revenue):
        """Test that revenue CTE includes proper joins."""
        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            grouping_columns=None,
        )

        cte_str = str(cte)
        # Should join assignments, rooms, and financial tables
        assert "occupancy_assignments_green" in cte_str
        assert "occupancy_rooms_green" in cte_str
        assert "financial_summary_blue" in cte_str
        # Should have JOIN clauses
        assert "JOIN" in cte_str

    def test_build_revenue_cte_with_group_profile_grouping(self, cte_revenue):
        """Test building revenue CTE with group profile grouping."""
        from app.models.occupancy import OccupancyAssignmentsGreen

        group_profile_grouping = {
            "organization_id": OccupancyAssignmentsGreen.organization_id,
            "property_id": OccupancyAssignmentsGreen.property_id,
            "stay_date": OccupancyAssignmentsGreen.stay_date,
            "group_profile_id": OccupancyAssignmentsGreen.group_profile_id,
            "group_profile_code": OccupancyAssignmentsGreen.group_profile_code,
        }

        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            grouping_columns=group_profile_grouping,
        )

        assert isinstance(cte, CTE)
        cte_str = str(cte)
        # Should include group profile columns in grouping
        assert "group_profile" in cte_str

    def test_build_revenue_cte_includes_all_financial_columns(self, cte_revenue):
        """Test that revenue CTE includes all financial columns."""
        cte = cte_revenue.build(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            grouping_columns=None,
        )

        cte_str = str(cte)
        # Should include all financial columns
        financial_columns = [
            "total_room_rate",
            "total_other_room_revenue",
            "total_room_revenue_adjustments",
            "total_room_revenue",
            "total_room_taxes",
            "total_room_fees",
            "total_other_revenue",
            "total_revenue",
        ]

        for column in financial_columns:
            assert column in cte_str
