"""
Tests for TraditionalQuery class.
"""

from datetime import date

import pytest
from sqlalchemy.sql.selectable import Select

from app.models.occupancy import OccupancyAndRevenue
from app.services.query_builders.traditional_query import TraditionalQuery


class TestTraditionalQuery:
    """Test cases for TraditionalQuery class."""

    @pytest.fixture
    def traditional_query(self):
        """Create a TraditionalQuery instance for testing."""
        return TraditionalQuery(
            property_ids=[208095, 208096],
            organization_id=208095,
            model=OccupancyAndRevenue,
        )

    def test_init(self, traditional_query):
        """Test TraditionalQuery initialization."""
        assert traditional_query.property_ids == [208095, 208096]
        assert traditional_query.organization_id == 208095
        assert traditional_query.model == OccupancyAndRevenue

    def test_build_basic_query(self, traditional_query):
        """Test building a basic traditional query."""
        selected_columns = [
            OccupancyAndRevenue.stay_date,
            OccupancyAndRevenue.property_id,
        ]
        group_by_expressions = [
            OccupancyAndRevenue.stay_date,
            OccupancyAndRevenue.property_id,
        ]

        query = traditional_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        # Verify the query has the expected structure
        query_str = str(query)
        assert "SELECT" in query_str
        assert "WHERE" in query_str
        assert "GROUP BY" in query_str

    def test_build_query_with_room_type_filter(self, traditional_query):
        """Test building query with room type filter."""
        selected_columns = [OccupancyAndRevenue.stay_date]
        group_by_expressions = [OccupancyAndRevenue.stay_date]

        query = traditional_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=[1, 2, 3],
            sort_columns=None,
        )

        query_str = str(query)
        assert "room_type_id" in query_str

    def test_build_query_with_sort(self, traditional_query):
        """Test building query with sort columns."""
        from sqlalchemy import asc

        selected_columns = [OccupancyAndRevenue.stay_date]
        group_by_expressions = [OccupancyAndRevenue.stay_date]
        sort_columns = [asc(OccupancyAndRevenue.stay_date)]

        query = traditional_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=sort_columns,
        )

        query_str = str(query)
        assert "ORDER BY" in query_str

    def test_get_filters(self, traditional_query):
        """Test filter generation."""
        filters = traditional_query._get_filters(
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=[1, 2],
        )

        # Should have organization, property, date range, and room type filters
        filter_str = str(filters)
        assert "organization_id" in filter_str
        assert "property_id" in filter_str
        assert "stay_date" in filter_str
        assert "room_type_id" in filter_str

    def test_get_filters_without_room_types(self, traditional_query):
        """Test filter generation without room type filter."""
        filters = traditional_query._get_filters(
            start_date=date(2025, 6, 18), end_date=date(2025, 7, 1), room_type_ids=None
        )

        filter_str = str(filters)
        assert "organization_id" in filter_str
        assert "property_id" in filter_str
        assert "stay_date" in filter_str
        # Should not have room_type_id filter when not provided
        assert "room_type_id" not in filter_str or "IN" not in filter_str
