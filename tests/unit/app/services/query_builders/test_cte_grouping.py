"""
Tests for CTEGrouping class.
"""

from sqlalchemy import literal_column

from app.models.occupancy import OccupancyRoomsGreen
from app.services.query_builders.cte_grouping import CTEGrouping


class TestCTEGrouping:
    """Test cases for CTEGrouping class."""

    def test_determine_columns_default(self):
        """Test default grouping columns without specific group by."""
        group_by_expressions = []

        result = CTEGrouping.determine_columns(group_by_expressions)

        expected_keys = {"organization_id", "property_id", "stay_date", "room_type_id"}
        assert set(result.keys()) == expected_keys
        assert result["organization_id"] == OccupancyRoomsGreen.organization_id
        assert result["property_id"] == OccupancyRoomsGreen.property_id
        assert result["stay_date"] == OccupancyRoomsGreen.stay_date
        assert result["room_type_id"] == OccupancyRoomsGreen.room_type_id

    def test_determine_columns_with_room_type(self):
        """Test grouping columns when grouping by room_type."""
        # Create mock expressions with room_type
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        group_by_expressions = [room_type_expr]

        result = CTEGrouping.determine_columns(group_by_expressions)

        expected_keys = {
            "organization_id",
            "property_id",
            "stay_date",
            "room_type_id",
            "room_type",
        }
        assert set(result.keys()) == expected_keys
        assert result["room_type"] == OccupancyRoomsGreen.room_type

    def test_determine_columns_with_group_profile(self):
        """Test grouping columns when grouping by group_profile_code (exclusive mode)."""
        # Create mock expressions with group_profile_code
        group_profile_expr = literal_column("group_profile_code_placeholder")
        group_profile_expr.name = "group_profile_code"

        group_by_expressions = [group_profile_expr]

        result = CTEGrouping.determine_columns(group_by_expressions)

        expected_keys = {
            "organization_id",
            "property_id",
            "stay_date",
            "group_profile_id",
            "group_profile_code",
        }
        assert set(result.keys()) == expected_keys
        assert result["group_profile_id"] == OccupancyRoomsGreen.group_profile_id
        assert result["group_profile_code"] == OccupancyRoomsGreen.group_profile_code
        # Should NOT have room_type_id in group profile mode
        assert "room_type_id" not in result

    def test_determine_columns_group_profile_exclusive(self):
        """Test that group_profile_code mode excludes room_type even if both are present."""
        # Create mock expressions with both group_profile_code and room_type
        group_profile_expr = literal_column("group_profile_code_placeholder")
        group_profile_expr.name = "group_profile_code"

        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        group_by_expressions = [group_profile_expr, room_type_expr]

        result = CTEGrouping.determine_columns(group_by_expressions)

        # Should use group profile mode and exclude room_type
        expected_keys = {
            "organization_id",
            "property_id",
            "stay_date",
            "group_profile_id",
            "group_profile_code",
        }
        assert set(result.keys()) == expected_keys
        assert "room_type_id" not in result
        assert "room_type" not in result

    def test_determine_columns_with_stay_date(self):
        """Test grouping columns when grouping by stay_date."""
        # Create mock expressions with stay_date
        stay_date_expr = literal_column("stay_date_placeholder")
        stay_date_expr.name = "stay_date"

        group_by_expressions = [stay_date_expr]

        result = CTEGrouping.determine_columns(group_by_expressions)

        # Should include default columns plus room_type_id (not group profile mode)
        expected_keys = {"organization_id", "property_id", "stay_date", "room_type_id"}
        assert set(result.keys()) == expected_keys

    def test_determine_columns_with_expression_without_name(self):
        """Test grouping columns with expressions that don't have name attribute."""

        # Create mock expression without name attribute
        class MockExpression:
            pass

        group_by_expressions = [MockExpression()]

        result = CTEGrouping.determine_columns(group_by_expressions)

        # Should use default grouping
        expected_keys = {"organization_id", "property_id", "stay_date", "room_type_id"}
        assert set(result.keys()) == expected_keys

    def test_determine_columns_mixed_expressions(self):
        """Test grouping columns with mix of named and unnamed expressions."""
        # Create mock expressions
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        class MockExpression:
            pass

        group_by_expressions = [room_type_expr, MockExpression()]

        result = CTEGrouping.determine_columns(group_by_expressions)

        # Should include room_type since it was detected
        expected_keys = {
            "organization_id",
            "property_id",
            "stay_date",
            "room_type_id",
            "room_type",
        }
        assert set(result.keys()) == expected_keys
