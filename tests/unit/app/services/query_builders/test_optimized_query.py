"""
Tests for OptimizedQuery class.
"""

from datetime import date

import pytest
from sqlalchemy import literal_column
from sqlalchemy.sql.selectable import Select

from app.services.query_builders.optimized_query import OptimizedQuery


class TestOptimizedQuery:
    """Test cases for OptimizedQuery class."""

    @pytest.fixture
    def optimized_query(self):
        """Create an OptimizedQuery instance for testing."""
        return OptimizedQuery(property_ids=[208095, 208096], organization_id=208095)

    def test_init(self, optimized_query):
        """Test OptimizedQuery initialization."""
        assert optimized_query.property_ids == [208095, 208096]
        assert optimized_query.organization_id == 208095
        assert optimized_query.capacity_cte is not None
        assert optimized_query.assignments_cte is not None
        assert optimized_query.revenue_cte is not None
        assert optimized_query.grouping is not None
        assert optimized_query.mapper is not None
        assert optimized_query.joiner is not None

    def test_build_basic_query(self, optimized_query):
        """Test building a basic optimized query."""
        # Create mock selected columns
        stay_date_col = literal_column("stay_date_placeholder")
        stay_date_col.name = "stay_date"

        capacity_col = literal_column("capacity_count_placeholder")
        capacity_col.name = "capacity_count"

        selected_columns = [stay_date_col, capacity_col]

        # Create mock group by expressions
        stay_date_expr = literal_column("stay_date_placeholder")
        stay_date_expr.name = "stay_date"

        group_by_expressions = [stay_date_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        # Verify the query has CTE structure
        query_str = str(query)
        assert "WITH" in query_str
        assert "capacity_aggregated" in query_str
        assert "assignments_aggregated" in query_str

    def test_build_query_with_room_type_grouping(self, optimized_query):
        """Test building query with room_type grouping."""
        # Create mock selected columns
        room_type_col = literal_column("room_type_placeholder")
        room_type_col.name = "room_type"

        capacity_col = literal_column("capacity_count_placeholder")
        capacity_col.name = "capacity_count"

        selected_columns = [room_type_col, capacity_col]

        # Create mock group by expressions with room_type
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        group_by_expressions = [room_type_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        assert "room_type" in query_str

    def test_build_query_with_group_profile_grouping(self, optimized_query):
        """Test building query with group_profile_code grouping."""
        # Create mock selected columns
        group_profile_col = literal_column("group_profile_code_placeholder")
        group_profile_col.name = "group_profile_code"

        capacity_col = literal_column("capacity_count_placeholder")
        capacity_col.name = "capacity_count"

        selected_columns = [group_profile_col, capacity_col]

        # Create mock group by expressions with group_profile_code
        group_profile_expr = literal_column("group_profile_code_placeholder")
        group_profile_expr.name = "group_profile_code"

        group_by_expressions = [group_profile_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        assert "group_profile" in query_str

    def test_build_query_with_room_type_filter(self, optimized_query):
        """Test building query with room type filter."""
        # Create mock selected columns
        stay_date_col = literal_column("stay_date_placeholder")
        stay_date_col.name = "stay_date"

        selected_columns = [stay_date_col]

        # Create mock group by expressions
        stay_date_expr = literal_column("stay_date_placeholder")
        stay_date_expr.name = "stay_date"

        group_by_expressions = [stay_date_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=[1, 2, 3],
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        # Should have room_type_id filter in the CTEs
        assert "room_type_id" in query_str

    def test_has_revenue_columns_detection(self, optimized_query):
        """Test detection of revenue columns."""
        # Test with revenue column
        revenue_col = literal_column("total_room_revenue_placeholder")
        revenue_col.name = "total_room_revenue"

        assert optimized_query._has_revenue_columns([revenue_col]) is True

        # Test with non-revenue column
        capacity_col = literal_column("capacity_count_placeholder")
        capacity_col.name = "capacity_count"

        assert optimized_query._has_revenue_columns([capacity_col]) is False

        # Test with mixed columns
        mixed_cols = [revenue_col, capacity_col]
        assert optimized_query._has_revenue_columns(mixed_cols) is True

    def test_build_query_with_revenue_columns(self, optimized_query):
        """Test building query with revenue columns uses revenue CTE."""
        # Create mock revenue columns
        revenue_col = literal_column("total_room_revenue_placeholder")
        revenue_col.name = "total_room_revenue"

        room_type_col = literal_column("room_type_placeholder")
        room_type_col.name = "room_type"

        selected_columns = [room_type_col, revenue_col]

        # Create mock group by expressions
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        group_by_expressions = [room_type_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        # Should use revenue CTE instead of capacity/assignments CTEs
        assert "revenue_aggregated" in query_str

    def test_build_query_with_adr_column(self, optimized_query):
        """Test building query with ADR column uses revenue CTE."""
        # Create mock ADR column
        adr_col = literal_column("adr_placeholder")
        adr_col.name = "adr"

        room_type_col = literal_column("room_type_placeholder")
        room_type_col.name = "room_type"

        selected_columns = [room_type_col, adr_col]

        # Create mock group by expressions
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"

        group_by_expressions = [room_type_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        # Should use revenue CTE for ADR calculation
        assert "revenue_aggregated" in query_str

    def test_build_query_with_all_financial_columns(self, optimized_query):
        """Test building query with all financial columns uses revenue CTE."""
        # Create mock financial columns
        financial_columns = [
            ("total_room_rate", "total_room_rate_placeholder"),
            ("total_other_room_revenue", "total_other_room_revenue_placeholder"),
            (
                "total_room_revenue_adjustments",
                "total_room_revenue_adjustments_placeholder",
            ),
            ("total_room_taxes", "total_room_taxes_placeholder"),
            ("total_room_fees", "total_room_fees_placeholder"),
            ("total_other_revenue", "total_other_revenue_placeholder"),
            ("total_revenue", "total_revenue_placeholder"),
        ]

        selected_columns = []
        for name, placeholder in financial_columns:
            col = literal_column(placeholder)
            col.name = name
            selected_columns.append(col)

        # Add room_type for grouping
        room_type_col = literal_column("room_type_placeholder")
        room_type_col.name = "room_type"
        selected_columns.append(room_type_col)

        # Create mock group by expressions
        room_type_expr = literal_column("room_type_placeholder")
        room_type_expr.name = "room_type"
        group_by_expressions = [room_type_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        # Should use revenue CTE for all financial columns
        assert "revenue_aggregated" in query_str

    def test_build_query_with_group_profile_code(self, optimized_query):
        """Test building query with group_profile_code uses revenue CTE correctly."""
        # Create mock columns
        group_profile_col = literal_column("group_profile_code_placeholder")
        group_profile_col.name = "group_profile_code"

        revenue_col = literal_column("total_room_revenue_placeholder")
        revenue_col.name = "total_room_revenue"

        selected_columns = [group_profile_col, revenue_col]

        # Create mock group by expressions
        group_profile_expr = literal_column("group_profile_code_placeholder")
        group_profile_expr.name = "group_profile_code"
        group_by_expressions = [group_profile_expr]

        query = optimized_query.build(
            selected_columns=selected_columns,
            group_by_expressions=group_by_expressions,
            start_date=date(2025, 6, 18),
            end_date=date(2025, 7, 1),
            room_type_ids=None,
            sort_columns=None,
        )

        assert isinstance(query, Select)
        query_str = str(query)
        assert "WITH" in query_str
        # Should use revenue CTE for group profile queries
        assert "revenue_aggregated" in query_str
