from unittest.mock import patch

import pandas as pd
import pytest
from pydantic import ValidationError

from app.enums.granularity import Granularity
from app.enums.sort import Sort
from app.schemas.query import QueryParams


class TestQueryParams:
    def test_valid_query(self):
        valid_data = {
            "organization_id": 1,
            "property_ids": [101, 102],
            "columns": ["adr", "revpar"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": ["stay_date"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }
        query = QueryParams(**valid_data)
        assert query.organization_id == 1
        assert query.property_ids == [101, 102]

    def test_valid_query_without_groups(self):
        valid_data = {
            "organization_id": 1,
            "property_ids": [101, 102],
            "columns": ["organization_id", "property_id"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "offset": 0,
            "limit": 100,
            "sort": [],
            "group_by": [],
        }
        query = QueryParams(**valid_data)
        assert query.organization_id == 1
        assert query.property_ids == [101, 102]

    def test_start_before_end(self):
        data = {
            "organization_id": 1,
            "property_ids": [101, 102],
            "columns": ["organization_id", "property_id"],
            "start_date": "2023-02-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": [],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }
        with pytest.raises(ValidationError):
            QueryParams(**data)

    def test_columns_and_groups_cannot_overlap(self):
        data = {
            "organization_id": 1,
            "property_ids": [101, 102],
            "columns": ["organization_id", "stay_date"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": ["stay_date"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }
        with pytest.raises(ValidationError):
            QueryParams(**data)

    @patch("app.services.query._")
    def test_process_results_translation_applied(self, mock_gettext, query_service):
        mock_gettext.side_effect = lambda value: f"{value.lower().replace('-', '_')}"
        df = pd.DataFrame(
            {
                "stay_date": ["2025-05-01", "2025-05-01"],
                "reservation_source": ["Phone", "Walk-In"],
                "total_rooms_sold": [2, 3],
            }
        )
        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date", "reservation_source"]
        )
        assert result == {
            "2025-05-01": {
                "phone": {"total_rooms_sold": 2},
                "walk_in": {"total_rooms_sold": 3},
            }
        }
        mock_gettext.assert_any_call("Phone")
        mock_gettext.assert_any_call("Walk-In")

    def test_sort_field_must_be_in_columns_valid(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["adr", "revpar"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": [],
            "offset": 0,
            "limit": 100,
            "sort": [Sort.adr_asc],
        }
        query = QueryParams(**data)
        assert query.sort == [Sort.adr_asc]

    def test_sort_mixed_valid_and_invalid_fields_raises(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["adr"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": [],
            "sort": [Sort.adr_asc, Sort.occupancy_desc],
            "offset": 0,
            "limit": 100,
        }
        with pytest.raises(ValidationError) as exc:
            QueryParams(**data)
        assert "must be included in 'columns'" in str(exc.value)

    def test_sort_empty_when_columns_present(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["adr"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": [],
            "sort": [],
            "offset": 0,
            "limit": 100,
        }
        query = QueryParams(**data)
        assert query.sort == []

    def test_valid_group_by_with_aggregate_columns(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["adr", "revpar", "occupancy"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "granularity": "day",
            "group_by": ["stay_date"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }
        query = QueryParams(**data)
        assert query.group_by[0].value == "stay_date"

    def test_non_aggregate_column_with_group_by_raises(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["room_id", "adr"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "granularity": "day",
            "group_by": ["stay_date"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }

        with pytest.raises(ValidationError) as exc:
            QueryParams(**data)

        assert "cannot be used with group by: room_id" in str(exc.value)

    def test_capacity_count_with_reservation_source_raises(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["capacity_count"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "granularity": "day",
            "group_by": ["reservation_source"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }

        with pytest.raises(ValidationError) as exc:
            QueryParams(**data)

        assert "cannot be used with group by: reservation_source" in str(exc.value)

    def test_sort_field_missing_from_columns_raises(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["adr"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "granularity": Granularity.day,
            "group_by": [],
            "sort": [Sort.revpar_desc],
            "offset": 0,
            "limit": 100,
        }
        with pytest.raises(ValidationError) as exc:
            QueryParams(**data)
        assert "must be included in 'columns'" in str(exc.value)

    def test_non_aggregate_column_allowed_with_group_by_room_type(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["room_type_id", "adr", "revpar"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "granularity": "day",
            "group_by": ["room_type"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }

        query = QueryParams(**data)
        assert "room_type_id" in [col.value for col in query.columns]

    def test_invalid_non_aggregate_column_with_group_by_still_fails(self):
        data = {
            "organization_id": 1,
            "property_ids": [101],
            "columns": ["group_profile_code", "adr"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "granularity": "day",
            "group_by": ["room_type"],
            "offset": 0,
            "limit": 100,
            "sort": [],
        }

        with pytest.raises(ValidationError) as exc:
            QueryParams(**data)

        assert "cannot be used with group by: group_profile_code" in str(exc.value)
