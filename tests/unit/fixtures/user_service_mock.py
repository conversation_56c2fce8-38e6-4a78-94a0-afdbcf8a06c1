from typing import List

import pytest
from pytest_mock.plugin import <PERSON><PERSON><PERSON><PERSON>ture


@pytest.fixture(scope="function")
def mock_user_service(mocker: MockerFixture):
    def configure_mock(
        property_ids: List[int] = None, organization_ids: List[int] = None
    ):
        if property_ids is None:
            property_ids = [1, 2, 3]
        if organization_ids is None:
            organization_ids = [100, 200]

        mock = mocker.patch("app.common.dependencies.UserService")
        mock.get_user_assignment.return_value = (property_ids, organization_ids)
        return mock

    return configure_mock
