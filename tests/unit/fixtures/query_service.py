from unittest.mock import AsyncMock, MagicMock

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.services.query import QueryService


@pytest.fixture
def fake_row():
    return {
        "stay_date": "2024-05-01",
        "organization_id": 177159,
        "property_id": 22425,
        "occupancy": 0.0,
        "room_type": "Single room",
    }


@pytest.fixture
def query_service(mocker: MockerFixture):
    # Mock cache to act as a no-op (it won't cache anything)
    cache_mock = AsyncMock()
    cache_mock.memoize.return_value = lambda f: f
    mocker.patch("app.services.query.cache", cache_mock)

    mock_db = MagicMock()
    return QueryService(database=mock_db, property_ids=[1, 2, 3], organization_id=123)
