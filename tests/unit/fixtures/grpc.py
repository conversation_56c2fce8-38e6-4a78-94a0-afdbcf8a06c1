import pytest
from cloudbeds.occupancy.v1 import occupancy_pb2
from google.type import date_pb2

from app.grpc.service.occupancy import OccupancyService


@pytest.fixture
def occupancy_service():
    return OccupancyService()


@pytest.fixture
def query_request():
    return occupancy_pb2.QueryRequest(
        organization_id=1,
        property_ids=[1, 2],
        start_date=date_pb2.Date(year=2023, month=1, day=1),
        end_date=date_pb2.Date(year=2023, month=1, day=31),
        granularity=occupancy_pb2.GRANULARITY_DAY,
        columns=[occupancy_pb2.Column.COLUMN_TOTAL_ROOM_REVENUE],
        group_by=[occupancy_pb2.GroupBy.GROUP_BY_ROOM_TYPE],
        room_type_ids=[1],
        offset=0,
        limit=10,
    )


@pytest.fixture
def query_reservation_sources_request():
    return occupancy_pb2.QueryReservationSourcesRequest(
        organization_id=1,
        property_ids=[1],
        start_date=date_pb2.Date(year=2023, month=1, day=1),
        end_date=date_pb2.Date(year=2023, month=1, day=31),
        granularity=occupancy_pb2.Granularity.GRANULARITY_DAY,
        room_type_ids=[1],
        offset=0,
        limit=10,
    )
