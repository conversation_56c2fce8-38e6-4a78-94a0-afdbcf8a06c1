import pytest
from cloudbeds.organization.v1 import property_pb2
from cloudbeds.user.v1 import user_pb2
from pytest_mock.plugin import Mo<PERSON><PERSON>ixture


@pytest.fixture(scope="function")
def user_service_client(mocker: Mo<PERSON>Fixture):
    def mock_client(assignment: user_pb2.GetUserAssignmentResponse):
        service_mock = mocker.Mock()
        service_mock.__enter__ = mocker.Mock(return_value=mocker.Mock())
        service_mock.__exit__ = mocker.Mock(return_value=mocker.Mock())
        service_mock.__enter__.return_value.get_assignment_by_user_id.return_value = (
            assignment
        )
        mocker.patch(
            "app.services.user_service.UserServiceClient",
            return_value=service_mock,
        )

    yield mock_client


@pytest.fixture(scope="function")
def property_service_client(mocker: MockerFixture):
    def mock_client(response: property_pb2.ListPropertiesResponse):
        service_mock = mocker.Mock()
        # For context manager usage
        service_mock.__enter__ = mocker.Mock(return_value=mocker.Mock())
        service_mock.__exit__ = mocker.Mock(return_value=mocker.Mock())
        service_mock.__enter__.return_value.get_properties_by_ids.return_value = (
            response
        )
        # For direct usage
        service_mock.get_properties_by_ids.return_value = response
        mocker.patch(
            "app.services.user_service.PropertyServiceClient",
            return_value=service_mock,
        )

    yield mock_client
