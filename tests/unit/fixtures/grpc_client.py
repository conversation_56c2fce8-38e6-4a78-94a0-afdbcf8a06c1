import grpc
import pytest
from pytest_mock.plugin import Mocker<PERSON><PERSON>ture

from tests.unit.data.parameters import PROPERTY_ID, PROPERTY_ID_2


class TestRpcError(grpc.RpcError):
    def __init__(self, code):
        self._code = code

    def code(self):
        return self._code

    def debug_error_string(self):
        return "TestRpcError"

    def exception(self):
        return


@pytest.fixture(scope="function")
def mock_grpc_client(mocker: MockerFixture):
    grpc_mock = mocker.Mock()
    grpc_mock.return_value.list_properties.return_value = dict(
        properties=[PROPERTY_ID, PROPERTY_ID_2]
    )
    mocker.patch("app.services.grpc.user_service_client.grpc", grpc_mock)
    mocker.patch("app.services.grpc.property_service_client.grpc", grpc_mock)


@pytest.fixture(scope="function")
def mock_broken_grpc_not_found(mocker: MockerFixture):
    mocker.patch(
        "app.services.grpc.property_service_client.property_pb2.ListPropertiesRequest",
        side_effect=TestRpcError(grpc.StatusCode.NOT_FOUND),
    )
    mocker.patch(
        "app.services.grpc.user_service_client.user_pb2.GetUserAssignmentRequest",
        side_effect=TestRpcError(grpc.StatusCode.NOT_FOUND),
    )


@pytest.fixture(scope="function")
def mock_broken_grpc_unavailable(mocker: MockerFixture):
    mocker.patch(
        "app.services.grpc.property_service_client.property_pb2.ListPropertiesRequest",
        side_effect=TestRpcError(grpc.StatusCode.UNAVAILABLE),
    )
    mocker.patch(
        "app.services.grpc.user_service_client.user_pb2.GetUserAssignmentRequest",
        side_effect=TestRpcError(grpc.StatusCode.UNAVAILABLE),
    )


@pytest.fixture(scope="function")
def mock_broken_grpc_generic(mocker: MockerFixture):
    mocker.patch(
        "app.services.grpc.property_service_client.property_pb2.ListPropertiesRequest",
        side_effect=Exception,
    )
    mocker.patch(
        "app.services.grpc.user_service_client.user_pb2.GetUserAssignmentRequest",
        side_effect=Exception,
    )
