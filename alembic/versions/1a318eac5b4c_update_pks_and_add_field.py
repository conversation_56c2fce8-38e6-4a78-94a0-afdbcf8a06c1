"""update PKs and add field

Revision ID: 1a318eac5b4c
Revises: 633b0e16dd9c
Create Date: 2025-03-24 08:30:37.766560

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1a318eac5b4c"
down_revision: Union[str, None] = "633b0e16dd9c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
            ADD COLUMN group_profile VARCHAR,
            ADD COLUMN group_profile_code VARCHAR;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms ADD CONSTRAINT occupancy_rooms_pkey PRIMARY KEY (organization_id,property_id,room_type_id,room_id,stay_date);
    """
    )

    op.execute("DROP VIEW occupancy.occupancy;")

    op.execute(
        """
        CREATE OR REPLACE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            financials.total_room_rate,
            financials.total_other_room_revenue,
            financials.total_room_revenue_adjustments,
            financials.total_room_revenue,
            financials.total_room_taxes,
            financials.total_room_fees,
            financials.total_other_revenue,
            financials.total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials
            ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.stay_date = financials.service_date;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
            DROP COLUMN group_profile,
            DROP COLUMN group_profile_code;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms ADD CONSTRAINT occupancy_rooms_pkey PRIMARY KEY (organization_id,property_id,room_type_id,room_id,stay_date);
    """
    )

    op.execute("DROP VIEW occupancy.occupancy;")

    op.execute(
        """
        CREATE OR REPLACE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            financials.total_room_rate,
            financials.total_other_room_revenue,
            financials.total_room_revenue_adjustments,
            financials.total_room_revenue,
            financials.total_room_taxes,
            financials.total_room_fees,
            financials.total_other_revenue,
            financials.total_revenue,
            rooms.group_profile_id
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials
            ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.stay_date = financials.service_date;
    """
    )
