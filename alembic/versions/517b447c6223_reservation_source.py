"""reservation_source column

Revision ID: 517b447c6223
Revises: 7226f8835d26
Create Date: 2025-02-17 10:20:23.149855

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "517b447c6223"
down_revision: Union[str, None] = "7226f8835d26"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_financials
        ADD COLUMN reservation_source VARCHAR NOT NULL DEFAULT '';
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_financials
        DROP COLUMN IF EXISTS reservation_source;
    """
    )
