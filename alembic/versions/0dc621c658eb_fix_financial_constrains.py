"""fix financial constrains

Revision ID: 0dc621c658eb
Revises: a0c8de9ba1c8
Create Date: 2025-02-20 15:03:48.223183

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0dc621c658eb"
down_revision: Union[str, None] = "a0c8de9ba1c8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_financials
        ALTER COLUMN reservation_source DROP NOT NULL;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_financials
        ALTER COLUMN reservation_source SET NOT NULL;
    """
    )
