"""Added initial migration

Revision ID: 185573b897b0
Revises:
Create Date: 2025-02-04 12:56:09.362336

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "185573b897b0"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "occupancy",
        sa.Column("organization_id", sa.BIGINT(), nullable=True),
        sa.Column("property_id", sa.BIGINT(), nullable=False),
        sa.Column("room_type_id", sa.BIGINT(), nullable=False),
        sa.Column("room_type", sa.VARCHAR(), nullable=True),
        sa.Column("stay_date", sa.DATE(), nullable=False),
        sa.Column("reservation_number", sa.VARCHAR(), nullable=True),
        sa.Column("booking_id", sa.BIGINT(), nullable=True),
        sa.Column("booking_room_id", sa.INTEGER(), nullable=True),
        sa.Column("room_rate", sa.FLOAT(), nullable=True),
        sa.Column("additional_room_revenue", sa.FLOAT(), nullable=True),
        sa.Column("room_revenue", sa.FLOAT(), nullable=True),
        sa.Column("adults_count", sa.INTEGER(), nullable=True),
        sa.Column("children_count", sa.INTEGER(), nullable=True),
        sa.Column("room_guest_count", sa.INTEGER(), nullable=True),
        sa.Column("rooms_sold", sa.INTEGER(), nullable=True),
        sa.Column("out_of_service_count", sa.INTEGER(), nullable=True),
        sa.Column("blocked_room_count", sa.INTEGER(), nullable=True),
        sa.Column("courtesy_hold_count", sa.INTEGER(), nullable=True),
        sa.Column("capacity_count", sa.INTEGER(), nullable=True),
        sa.Column("non_room_revenue", sa.FLOAT(), nullable=True),
        sa.Column("room_taxes", sa.FLOAT(), nullable=True),
        sa.Column("room_fees", sa.FLOAT(), nullable=True),
        sa.PrimaryKeyConstraint("property_id", "room_type_id", "stay_date"),
        schema="occupancy",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the table first
    op.drop_table("occupancy", schema="occupancy")
    # ### end Alembic commands ###
