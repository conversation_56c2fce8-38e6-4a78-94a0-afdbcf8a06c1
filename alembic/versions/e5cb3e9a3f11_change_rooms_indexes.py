"""change_financials_indexes

Revision ID: e5cb3e9a3f11
Revises: 0067057a1fd6
Create Date: 2025-04-14 19:13:10.545191

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e5cb3e9a3f11"
down_revision: Union[str, None] = "0067057a1fd6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        "CREATE INDEX IF NOT EXISTS occupancy_rooms_idx1 ON occupancy.occupancy_rooms(organization_id, property_id);"
    )
    op.execute(
        "ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;"
    )
    op.execute(
        "ALTER TABLE occupancy.occupancy_rooms ADD PRIMARY KEY (organization_id,property_id,room_type_id,room_id,stay_date);"
    )


def downgrade() -> None:
    op.execute(
        "ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;"
    )
    op.execute(
        "ALTER TABLE occupancy.occupancy_rooms ADD CONSTRAINT occupancy_rooms_pkey PRIMARY KEY (organization_id,property_id,room_type_id,room_id,booking_id, booking_room_id, stay_date);"
    )
