"""drop_reservation_source_from_rooms

Revision ID: 0067057a1fd6
Revises: c7eeaaa8f0ee
Create Date: 2025-03-26 20:05:52.298994

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0067057a1fd6"
down_revision: Union[str, None] = "c7eeaaa8f0ee"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        "ALTER TABLE occupancy.occupancy_rooms DROP COLUMN IF EXISTS reservation_source;"
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ADD COLUMN reservation_source VARCHAR NOT NULL DEFAULT '';
    """
    )
