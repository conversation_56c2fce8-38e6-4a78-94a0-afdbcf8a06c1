"""add_booking_id_to_view

Revision ID: c7eeaaa8f0ee
Revises: 89146255caab
Create Date: 2025-03-25 14:22:01.729742

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c7eeaaa8f0ee"
down_revision: Union[str, None] = "89146255caab"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("DROP VIEW occupancy.occupancy;")

    op.execute(
        """
        CREATE OR REPLACE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            financials.total_room_rate,
            financials.total_other_room_revenue,
            financials.total_room_revenue_adjustments,
            financials.total_room_revenue,
            financials.total_room_taxes,
            financials.total_room_fees,
            financials.total_other_revenue,
            financials.total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials
            ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.booking_id = financials.booking_id
            AND rooms.stay_date = financials.service_date;
    """
    )


def downgrade() -> None:
    op.execute("DROP VIEW occupancy.occupancy;")

    op.execute(
        """
        CREATE OR REPLACE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            financials.total_room_rate,
            financials.total_other_room_revenue,
            financials.total_room_revenue_adjustments,
            financials.total_room_revenue,
            financials.total_room_taxes,
            financials.total_room_fees,
            financials.total_other_revenue,
            financials.total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials
            ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.stay_date = financials.service_date;
    """
    )
