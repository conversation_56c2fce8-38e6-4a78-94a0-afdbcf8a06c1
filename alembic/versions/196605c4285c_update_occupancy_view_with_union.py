"""update_occupancy_view_with_union

Revision ID: 196605c4285c
Revises: daab367aa986
Create Date: 2025-04-24 16:31:08.825539

"""
from typing import Sequence, Union

from alembic import op

revision: str = "196605c4285c"
down_revision: Union[str, None] = "daab367aa986"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("DROP VIEW IF EXISTS occupancy.occupancy;")

    op.execute(
        """
        CREATE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            CASE
                WHEN rooms.inventory_kind::text = 'Physical'::text
                AND rooms.room_name::text <> 'UNASSIGNED_ROOM'::text THEN 1
                ELSE 0
            END AS total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            COALESCE(financials.total_room_rate, 0::double precision) AS total_room_rate,
            COALESCE(financials.total_other_room_revenue, 0::double precision) AS total_other_room_revenue,
            COALESCE(financials.total_room_revenue_adjustments, 0::double precision) AS total_room_revenue_adjustments,
            COALESCE(financials.total_room_revenue, 0::double precision) AS total_room_revenue,
            COALESCE(financials.total_room_taxes, 0::double precision) AS total_room_taxes,
            COALESCE(financials.total_room_fees, 0::double precision) AS total_room_fees,
            COALESCE(financials.total_other_revenue, 0::double precision) AS total_other_revenue,
            COALESCE(financials.total_revenue, 0::double precision) AS total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.booking_id = financials.booking_id
            AND rooms.stay_date = financials.service_date
        WHERE
            rooms.room_name::text <> 'UNASSIGNED_ROOM'::character varying::text

        UNION ALL

        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            CASE
                WHEN rooms.inventory_kind::text = 'Physical'::text
                AND rooms.room_name::text <> 'UNASSIGNED_ROOM'::text THEN 1
                ELSE 0
            END AS total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            COALESCE(financials.total_room_rate, 0::double precision) AS total_room_rate,
            COALESCE(financials.total_other_room_revenue, 0::double precision) AS total_other_room_revenue,
            COALESCE(financials.total_room_revenue_adjustments, 0::double precision) AS total_room_revenue_adjustments,
            COALESCE(financials.total_room_revenue, 0::double precision) AS total_room_revenue,
            COALESCE(financials.total_room_taxes, 0::double precision) AS total_room_taxes,
            COALESCE(financials.total_room_fees, 0::double precision) AS total_room_fees,
            COALESCE(financials.total_other_revenue, 0::double precision) AS total_other_revenue,
            COALESCE(financials.total_revenue, 0::double precision) AS total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.stay_date = financials.service_date
        WHERE
            rooms.room_name::text = 'UNASSIGNED_ROOM'::text;
        """
    )


def downgrade() -> None:
    op.execute("DROP VIEW IF EXISTS occupancy.occupancy;")

    op.execute(
        """
        CREATE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.room_type_short_title,
            rooms.room_name,
            rooms.stay_date,
            financials.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            COALESCE(financials.total_room_rate, 0) AS total_room_rate,
            COALESCE(financials.total_other_room_revenue, 0) AS total_other_room_revenue,
            COALESCE(financials.total_room_revenue_adjustments, 0) AS total_room_revenue_adjustments,
            COALESCE(financials.total_room_revenue, 0) AS total_room_revenue,
            COALESCE(financials.total_room_taxes, 0) AS total_room_taxes,
            COALESCE(financials.total_room_fees, 0) AS total_room_fees,
            COALESCE(financials.total_other_revenue, 0) AS total_other_revenue,
            COALESCE(financials.total_revenue, 0) AS total_revenue,
            rooms.group_profile_id,
            rooms.group_profile,
            rooms.group_profile_code
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.booking_id = financials.booking_id
            AND rooms.stay_date = financials.service_date;
        """
    )
