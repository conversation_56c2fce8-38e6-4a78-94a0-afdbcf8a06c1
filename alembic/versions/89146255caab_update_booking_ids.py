"""update booking ids

Revision ID: 89146255caab
Revises: 1a318eac5b4c
Create Date: 2025-03-24 23:03:29.811873

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "89146255caab"
down_revision: Union[str, None] = "1a318eac5b4c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
            ADD COLUMN booking_id BIGINT;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms ADD CONSTRAINT occupancy_rooms_pkey PRIMARY KEY (organization_id,property_id,room_type_id,room_id,booking_id, booking_room_id, stay_date);
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
            DROP COLUMN booking_id;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms DROP CONSTRAINT occupancy_rooms_pkey;
    """
    )

    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms ADD CONSTRAINT occupancy_rooms_pkey PRIMARY KEY (organization_id,property_id,room_type_id,room_id, stay_date);
    """
    )
