"""Occupancy view

Revision ID: 7226f8835d26
Revises: 185573b897b0
Create Date: 2025-02-14 10:20:23.149855

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7226f8835d26"
down_revision: Union[str, None] = "185573b897b0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("DROP TABLE IF EXISTS occupancy.occupancy;")
    op.execute(
        """
        CREATE TABLE occupancy.occupancy_rooms (
            organization_id BIGINT NOT NULL,
            property_id BIGINT NOT NULL,
            room_type_id BIGINT NOT NULL,
            room_type VARCHAR NOT NULL,
            room_id VARCHAR NOT NULL,
            inventory_kind VARCHAR NOT NULL,
            room_status VARCHAR NOT NULL,
            booking_room_id BIGINT,
            stay_date DATE NOT NULL,
            reservation_source VARCHAR NOT NULL,
            total_rooms_sold INT,
            out_of_service_count INT,
            blocked_rooms_count INT,
            total_rooms_available INT,
            capacity_count INT,
            adults_count INT,
            children_count INT,
            guest_count INT,
            PRIMARY KEY (organization_id, property_id, room_type_id, room_id, stay_date)
        ) PARTITION BY HASH (organization_id);
    """
    )

    for i in range(10):
        op.execute(
            f"""
            CREATE TABLE IF NOT EXISTS partitions.occupancy_rooms_tbl_{i}
            PARTITION OF occupancy.occupancy_rooms
            FOR VALUES WITH (MODULUS 10, REMAINDER {i});
        """
        )

    op.execute(
        """
        CREATE TABLE occupancy.occupancy_financials (
            organization_id BIGINT NOT NULL,
            property_id BIGINT NOT NULL,
            booking_id BIGINT,
            reservation_number VARCHAR,
            room_type_id BIGINT,
            room_type_name VARCHAR,
            booking_rooms_id BIGINT,
            service_date DATE,
            total_room_rate FLOAT,
            total_other_room_revenue FLOAT,
            total_room_revenue_adjustments FLOAT,
            total_room_revenue FLOAT,
            total_room_taxes FLOAT,
            total_room_fees FLOAT,
            total_other_revenue FLOAT,
            total_revenue FLOAT,
            PRIMARY KEY (organization_id, property_id, booking_rooms_id, service_date)
        ) PARTITION BY HASH (organization_id);
    """
    )

    for i in range(10):
        op.execute(
            f"""
            CREATE TABLE IF NOT EXISTS partitions.occupancy_financials_tbl_{i}
            PARTITION OF occupancy.occupancy_financials
            FOR VALUES WITH (MODULUS 10, REMAINDER {i});
        """
        )

    op.execute(
        """
        CREATE OR REPLACE VIEW occupancy.occupancy AS
        SELECT
            rooms.organization_id,
            rooms.property_id,
            rooms.room_type_id,
            rooms.room_id,
            rooms.room_type,
            rooms.stay_date,
            rooms.reservation_source,
            rooms.capacity_count,
            rooms.total_rooms_available,
            rooms.total_rooms_sold,
            rooms.blocked_rooms_count,
            rooms.out_of_service_count,
            rooms.adults_count,
            rooms.children_count,
            rooms.guest_count,
            financials.total_room_rate,
            financials.total_other_room_revenue,
            financials.total_room_revenue_adjustments,
            financials.total_room_revenue,
            financials.total_room_taxes,
            financials.total_room_fees,
            financials.total_other_revenue,
            financials.total_revenue
        FROM
            occupancy.occupancy_rooms rooms
            LEFT JOIN occupancy.occupancy_financials financials
            ON rooms.organization_id = financials.organization_id
            AND rooms.property_id = financials.property_id
            AND rooms.room_type_id = financials.room_type_id
            AND rooms.booking_room_id = financials.booking_rooms_id
            AND rooms.stay_date = financials.service_date;
    """
    )


def downgrade() -> None:
    op.execute("DROP VIEW IF EXISTS occupancy.occupancy;")
    for i in range(10):
        op.execute(f"DROP TABLE IF EXISTS partitions.occupancy_financials_tbl_{i};")
    op.execute("DROP TABLE IF EXISTS occupancy.occupancy_financials;")
    for i in range(10):
        op.execute(f"DROP TABLE IF EXISTS partitions.occupancy_rooms_tbl_{i};")
    op.execute("DROP TABLE IF EXISTS occupancy.occupancy_rooms;")
