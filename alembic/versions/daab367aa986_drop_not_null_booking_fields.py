"""drop_not_null_booking_fields

Revision ID: daab367aa986
Revises: e5cb3e9a3f11
Create Date: 2025-04-24 12:52:23.610111

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "daab367aa986"
down_revision: Union[str, None] = "e5cb3e9a3f11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN booking_room_id DROP NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN booking_id DROP NOT NULL;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN booking_room_id SET NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN booking_id SET NOT NULL;
    """
    )
