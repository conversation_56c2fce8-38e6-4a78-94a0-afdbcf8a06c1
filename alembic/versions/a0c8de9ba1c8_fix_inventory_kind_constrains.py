"""fix inventory kind constrains

Revision ID: a0c8de9ba1c8
Revises: 923ccb27cec2
Create Date: 2025-02-20 13:32:24.756075

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a0c8de9ba1c8"
down_revision: Union[str, None] = "923ccb27cec2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_type DROP NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_type_short_title DROP NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_name DROP NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN inventory_kind DROP NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_status DROP NOT NULL;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_type SET NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_type_short_title SET NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_name SET NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN inventory_kind SET NOT NULL;
    """
    )
    op.execute(
        """
        ALTER TABLE occupancy.occupancy_rooms
        ALTER COLUMN room_status SET NOT NULL;
    """
    )
