# DD
DD_TRACE_ENABLED=false

# DB
DATABASE_USER=database
DATABASE_NAME=database
DATABASE_SCHEMA=occupancy
DATABASE_PASSWORD=database
DATABASE_HOST_WRITER=occupancy-database
DATABASE_HOST_READER=occupancy-database
DATABASE_PORT=5433
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=5
DATABASE_POOL_RECYCLE=3600

# Redis
REDIS_HOST=occupancy-redis
REDIS_PORT=6379

# LaunchDarkly
LAUNCH_DARKLY_SDK_KEY=${LAUNCH_DARKLY_SDK_KEY}

# Calendar script
CALENDAR_TOPIC="us1.dictionary.calendar"
CALENDAR_START_DATE="2014-01-01"
CALENDAR_END_DATE="2045-12-31"
KAFKA_BROKER=pkc-pgq85.us-west-2.aws.confluent.cloud:9092
KAFKA_USERNAME=
KAFKA_PASSWORD=
SCHEMA_REGISTRY_URL=https://psrc-vn38j.us-east-2.aws.confluent.cloud
SCHEMA_REGISTRY_USERNAME=
SCHEMA_REGISTRY_PASSWORD=
