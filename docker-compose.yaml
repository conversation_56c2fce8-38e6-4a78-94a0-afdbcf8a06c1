services:
  occupancy-database:
    container_name: occupancy-database
    extra_hosts:
      - host.docker.internal:host-gateway
    image: postgres:16.4
    expose:
      - "5433"
    ports:
      - "5433:5433"
    command: -p 5433
    volumes:
      - occupancy-database:/var/lib/postgresql/data
      - ./alembic/init.sql:/docker-entrypoint-initdb.d/init.sql
    hostname: occupancy-database
    environment:
      - POSTGRES_PASSWORD=database
      - POSTGRES_USER=database
      - POSTGRES_DB=database
      - POSTGRES_PORT=5433
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U database -p 5433"]
      interval: 5s
      timeout: 3s
      retries: 5

  occupancy-redis:
    container_name: occupancy-redis
    extra_hosts:
      - host.docker.internal:host-gateway
    image: redis:8.0.1
    expose:
      - "6379"
    ports:
      - "6379:6379"
    volumes:
      - occupancy-redis:/data
    command: ["redis-server", "--appendonly", "yes", "--port", "6379"]
    hostname: occupancy-redis
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  occupancy-app:
    container_name: occupancy-app
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
      args:
        - GITHUB_ACCESS_TOKEN

    volumes:
      - .:/app
    # command: /app/.venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir app
    ports:
      - 8000:8000
    env_file:
      - .env
    depends_on:
      occupancy-database:
        condition: service_healthy
      occupancy-redis:
        condition: service_healthy

  occupancy-grpc:
    container_name: occupancy-grpc
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
      args:
        - GITHUB_ACCESS_TOKEN
    command: |
      uv run app/grpc/server.py
    volumes:
      - .:/app
    ports:
      - 50051:50051
    env_file:
      - .env
    depends_on:
      occupancy-database:
        condition: service_healthy

volumes:
  occupancy-database:
  occupancy-redis:
